<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AndroidLayouts">
    <shared>
      <config />
    </shared>
    <layouts>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout-sw600dp/fragment_accountview.xml">
        <config>
          <state>Portrait</state>
          <theme>@style/AppTheme</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout-sw600dp/fragment_companysetup.xml">
        <config>
          <state>Portrait</state>
          <theme>@style/AppTheme</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout-sw600dp/fragment_completesetup.xml">
        <config>
          <state>Portrait</state>
          <theme>@style/AppTheme</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout-sw600dp/fragment_dashboardview.xml">
        <config>
          <state>Portrait</state>
          <theme>@style/AppTheme</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout-sw600dp/fragment_home.xml">
        <config>
          <state>Portrait</state>
          <theme>@style/AppTheme</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout-sw600dp/fragment_subscription.xml">
        <config>
          <state>Portrait</state>
          <theme>@style/AppTheme</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/bottom_add_draw.xml">
        <config>
          <theme>@style/AppTheme</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/bottom_add_lineal.xml">
        <config>
          <theme>@style/AppTheme</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/bottom_select_customer.xml">
        <config>
          <theme>@style/AppTheme</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/bottom_sheet_month_filter.xml">
        <config>
          <theme>@style/AppTheme</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/bottom_sheet_multi_select_status_filter.xml">
        <config>
          <theme>@style/AppTheme</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/fragment_add_line_items.xml">
        <config>
          <theme>@style/AppTheme</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/fragment_companysetup.xml">
        <config>
          <theme>@style/AppTheme</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/fragment_completesetup.xml">
        <config>
          <theme>@style/AppTheme</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/fragment_create_customer.xml">
        <config>
          <theme>@style/AppTheme</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/fragment_create_estimation.xml">
        <config>
          <theme>@style/AppTheme</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/fragment_dashboardview.xml">
        <config>
          <theme>@style/AppTheme</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/fragment_draws.xml">
        <config>
          <theme>@style/AppTheme</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/fragment_home.xml">
        <config>
          <theme>@style/AppTheme</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/fragment_line_items.xml">
        <config>
          <theme>@style/AppTheme</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/fragment_login.xml">
        <config>
          <theme>@style/AppTheme</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/fragment_material_line_item.xml">
        <config>
          <theme>@style/AppTheme</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/fragment_profileview.xml">
        <config>
          <theme>@style/AppTheme</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/fragment_subscription.xml">
        <config>
          <theme>@style/AppTheme</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/header.xml">
        <config>
          <theme>@style/AppTheme</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/item_dashboard_project.xml">
        <config>
          <theme>@style/AppTheme</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/item_draw.xml">
        <config>
          <theme>@style/AppTheme</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/item_line_material.xml">
        <config>
          <theme>@style/AppTheme</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/item_line_total.xml">
        <config>
          <theme>@style/AppTheme</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/menu/bottom_nav_menu.xml">
        <config>
          <theme>@style/AppTheme</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/navigation/mobile_navigation.xml">
        <config>
          <theme>@style/AppTheme</theme>
        </config>
      </layout>
    </layouts>
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="NONE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="7629a6d9-4a69-4249-9449-416164fda9ed" name="Changes" comment="">
      <change afterPath="$PROJECT_DIR$/app/src/main/res/drawable/green_check_mark.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.gradle/8.5/executionHistory/executionHistory.bin" beforeDir="false" afterPath="$PROJECT_DIR$/.gradle/8.5/executionHistory/executionHistory.bin" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.gradle/8.5/executionHistory/executionHistory.lock" beforeDir="false" afterPath="$PROJECT_DIR$/.gradle/8.5/executionHistory/executionHistory.lock" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.gradle/8.5/fileHashes/fileHashes.bin" beforeDir="false" afterPath="$PROJECT_DIR$/.gradle/8.5/fileHashes/fileHashes.bin" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.gradle/8.5/fileHashes/fileHashes.lock" beforeDir="false" afterPath="$PROJECT_DIR$/.gradle/8.5/fileHashes/fileHashes.lock" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.gradle/8.5/fileHashes/resourceHashesCache.bin" beforeDir="false" afterPath="$PROJECT_DIR$/.gradle/8.5/fileHashes/resourceHashesCache.bin" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.gradle/buildOutputCleanup/buildOutputCleanup.lock" beforeDir="false" afterPath="$PROJECT_DIR$/.gradle/buildOutputCleanup/buildOutputCleanup.lock" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.gradle/buildOutputCleanup/outputFiles.bin" beforeDir="false" afterPath="$PROJECT_DIR$/.gradle/buildOutputCleanup/outputFiles.bin" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.gradle/file-system.probe" beforeDir="false" afterPath="$PROJECT_DIR$/.gradle/file-system.probe" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/generated/data_binding_base_class_source_out/debug/out/Manaknight/databinding/BottomSelectCustomerBinding.java" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/generated/data_binding_base_class_source_out/debug/out/Manaknight/databinding/BottomSelectCustomerBinding.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/generated/data_binding_base_class_source_out/debug/out/Manaknight/databinding/FragmentCreateEstimationBinding.java" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/generated/data_binding_base_class_source_out/debug/out/Manaknight/databinding/FragmentCreateEstimationBinding.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/generated/data_binding_base_class_source_out/debug/out/Manaknight/databinding/FragmentLineItemsBinding.java" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/generated/data_binding_base_class_source_out/debug/out/Manaknight/databinding/FragmentLineItemsBinding.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/generated/data_binding_base_class_source_out/debug/out/Manaknight/databinding/FragmentLoginBinding.java" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/generated/data_binding_base_class_source_out/debug/out/Manaknight/databinding/FragmentLoginBinding.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/generated/source/navigation-args/debug/com/manaknight/app/ui/fragments/InvoiceFragmentDirections.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/generated/source/navigation-args/debug/com/manaknight/app/ui/fragments/InvoiceFragmentDirections.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/apk/debug/com.manaknight.app-1.0.0.apk" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/apk/debug/com.manaknight.app-1.0.0.apk" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/compile_and_runtime_not_namespaced_r_class_jar/debug/processDebugResources/R.jar" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/compile_and_runtime_not_namespaced_r_class_jar/debug/processDebugResources/R.jar" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/data_binding_base_class_log_artifact/debug/dataBindingGenBaseClassesDebug/out/Manaknight-binding_classes.json" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/data_binding_base_class_log_artifact/debug/dataBindingGenBaseClassesDebug/out/Manaknight-binding_classes.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/data_binding_layout_info_type_merge/debug/mergeDebugResources/out/bottom_select_customer-layout.xml" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/data_binding_layout_info_type_merge/debug/mergeDebugResources/out/bottom_select_customer-layout.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/data_binding_layout_info_type_merge/debug/mergeDebugResources/out/fragment_create_customer-layout.xml" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/data_binding_layout_info_type_merge/debug/mergeDebugResources/out/fragment_create_customer-layout.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/data_binding_layout_info_type_merge/debug/mergeDebugResources/out/fragment_create_estimation-layout.xml" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/data_binding_layout_info_type_merge/debug/mergeDebugResources/out/fragment_create_estimation-layout.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/data_binding_layout_info_type_merge/debug/mergeDebugResources/out/fragment_material_line_item-layout.xml" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/data_binding_layout_info_type_merge/debug/mergeDebugResources/out/fragment_material_line_item-layout.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/data_binding_layout_info_type_merge/debug/mergeDebugResources/out/item_line_material-layout.xml" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/data_binding_layout_info_type_merge/debug/mergeDebugResources/out/item_line_material-layout.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/data_binding_layout_info_type_package/debug/packageDebugResources/out/bottom_select_customer-layout.xml" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/data_binding_layout_info_type_package/debug/packageDebugResources/out/bottom_select_customer-layout.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/data_binding_layout_info_type_package/debug/packageDebugResources/out/fragment_create_customer-layout.xml" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/data_binding_layout_info_type_package/debug/packageDebugResources/out/fragment_create_customer-layout.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/data_binding_layout_info_type_package/debug/packageDebugResources/out/fragment_create_estimation-layout.xml" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/data_binding_layout_info_type_package/debug/packageDebugResources/out/fragment_create_estimation-layout.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/data_binding_layout_info_type_package/debug/packageDebugResources/out/fragment_material_line_item-layout.xml" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/data_binding_layout_info_type_package/debug/packageDebugResources/out/fragment_material_line_item-layout.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/data_binding_layout_info_type_package/debug/packageDebugResources/out/item_line_material-layout.xml" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/data_binding_layout_info_type_package/debug/packageDebugResources/out/item_line_material-layout.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/dex/debug/mergeProjectDexDebug/0/classes.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/dex/debug/mergeProjectDexDebug/0/classes.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/dex/debug/mergeProjectDexDebug/1/classes.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/dex/debug/mergeProjectDexDebug/1/classes.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/dex/debug/mergeProjectDexDebug/10/classes.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/dex/debug/mergeProjectDexDebug/10/classes.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/dex/debug/mergeProjectDexDebug/12/classes.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/dex/debug/mergeProjectDexDebug/12/classes.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/dex/debug/mergeProjectDexDebug/13/classes.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/dex/debug/mergeProjectDexDebug/13/classes.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/dex/debug/mergeProjectDexDebug/2/classes.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/dex/debug/mergeProjectDexDebug/2/classes.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/dex/debug/mergeProjectDexDebug/3/classes.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/dex/debug/mergeProjectDexDebug/3/classes.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/dex/debug/mergeProjectDexDebug/4/classes.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/dex/debug/mergeProjectDexDebug/4/classes.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/dex/debug/mergeProjectDexDebug/5/classes.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/dex/debug/mergeProjectDexDebug/5/classes.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/dex/debug/mergeProjectDexDebug/9/classes.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/dex/debug/mergeProjectDexDebug/9/classes.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/dex_archive_input_jar_hashes/debug/dexBuilderDebug/out" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/dex_archive_input_jar_hashes/debug/dexBuilderDebug/out" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/incremental/dataBindingGenBaseClassesDebug/base_builder_log.json" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/incremental/dataBindingGenBaseClassesDebug/base_builder_log.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/incremental/debug/mergeDebugResources/compile-file-map.properties" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/incremental/debug/mergeDebugResources/compile-file-map.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/incremental/debug/mergeDebugResources/merger.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/incremental/debug/mergeDebugResources/stripped.dir/layout-sw600dp-v13/bottom_add_lineal.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/incremental/debug/mergeDebugResources/stripped.dir/layout-sw600dp-v13/bottom_add_material.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/incremental/debug/mergeDebugResources/stripped.dir/layout-sw600dp-v13/fragment_companysetup.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/incremental/debug/mergeDebugResources/stripped.dir/layout-sw600dp-v13/item_line_material.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/incremental/debug/mergeDebugResources/stripped.dir/layout/bottom_add_material.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/incremental/debug/mergeDebugResources/stripped.dir/layout/fragment_companysetup.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/incremental/debug/packageDebugResources/merger.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/incremental/debug/packageDebugResources/stripped.dir/layout-sw600dp-v13/bottom_add_lineal.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/incremental/debug/packageDebugResources/stripped.dir/layout-sw600dp-v13/bottom_add_material.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/incremental/debug/packageDebugResources/stripped.dir/layout-sw600dp-v13/fragment_companysetup.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/incremental/debug/packageDebugResources/stripped.dir/layout-sw600dp-v13/item_line_material.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/incremental/debug/packageDebugResources/stripped.dir/layout/bottom_add_material.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/incremental/debug/packageDebugResources/stripped.dir/layout/fragment_companysetup.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/incremental/packageDebug/tmp/debug/dex-renamer-state.txt" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/incremental/packageDebug/tmp/debug/dex-renamer-state.txt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/incremental/packageDebug/tmp/debug/zip-cache/androidResources" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/incremental/packageDebug/tmp/debug/zip-cache/androidResources" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/Manaknight/databinding/BottomSelectCustomerBinding.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/Manaknight/databinding/BottomSelectCustomerBinding.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/Manaknight/databinding/FragmentCreateEstimationBinding.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/Manaknight/databinding/FragmentCreateEstimationBinding.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/local_only_symbol_list/debug/parseDebugLocalResources/R-def.txt" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/local_only_symbol_list/debug/parseDebugLocalResources/R-def.txt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/merged_res/debug/mergeDebugResources/drawable_custom_checkbox.xml.flat" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/merged_res/debug/mergeDebugResources/drawable_custom_checkbox.xml.flat" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/merged_res/debug/mergeDebugResources/drawable_custom_checkbox_selector.xml.flat" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/merged_res/debug/mergeDebugResources/drawable_custom_checkbox_selector.xml.flat" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/merged_res/debug/mergeDebugResources/layout_bottom_select_customer.xml.flat" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/merged_res/debug/mergeDebugResources/layout_bottom_select_customer.xml.flat" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/merged_res/debug/mergeDebugResources/layout_fragment_add_line_items.xml.flat" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/merged_res/debug/mergeDebugResources/layout_fragment_add_line_items.xml.flat" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/merged_res/debug/mergeDebugResources/layout_fragment_create_customer.xml.flat" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/merged_res/debug/mergeDebugResources/layout_fragment_create_customer.xml.flat" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/merged_res/debug/mergeDebugResources/layout_fragment_create_estimation.xml.flat" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/merged_res/debug/mergeDebugResources/layout_fragment_create_estimation.xml.flat" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/merged_res/debug/mergeDebugResources/layout_fragment_home.xml.flat" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/merged_res/debug/mergeDebugResources/layout_fragment_home.xml.flat" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/merged_res/debug/mergeDebugResources/layout_fragment_material_line_item.xml.flat" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/merged_res/debug/mergeDebugResources/layout_fragment_material_line_item.xml.flat" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/merged_res/debug/mergeDebugResources/layout_item_line_material.xml.flat" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/merged_res/debug/mergeDebugResources/layout_item_line_material.xml.flat" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/mergeDebugResources.json" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/mergeDebugResources.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values.json" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/single/layout-sw600dp-v13.json" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/single/layout-sw600dp-v13.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/single/layout.json" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/single/layout.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/single/mergeDebugResources.json" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/single/mergeDebugResources.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/packaged_res/debug/packageDebugResources/drawable/custom_checkbox.xml" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/packaged_res/debug/packageDebugResources/drawable/custom_checkbox.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/packaged_res/debug/packageDebugResources/drawable/custom_checkbox_selector.xml" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/packaged_res/debug/packageDebugResources/drawable/custom_checkbox_selector.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/packaged_res/debug/packageDebugResources/layout/bottom_select_customer.xml" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/packaged_res/debug/packageDebugResources/layout/bottom_select_customer.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/packaged_res/debug/packageDebugResources/layout/fragment_add_line_items.xml" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/packaged_res/debug/packageDebugResources/layout/fragment_add_line_items.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/packaged_res/debug/packageDebugResources/layout/fragment_create_customer.xml" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/packaged_res/debug/packageDebugResources/layout/fragment_create_customer.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/packaged_res/debug/packageDebugResources/layout/fragment_create_estimation.xml" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/packaged_res/debug/packageDebugResources/layout/fragment_create_estimation.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/packaged_res/debug/packageDebugResources/layout/fragment_material_line_item.xml" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/packaged_res/debug/packageDebugResources/layout/fragment_material_line_item.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/packaged_res/debug/packageDebugResources/layout/item_line_material.xml" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/packaged_res/debug/packageDebugResources/layout/item_line_material.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/packaged_res/debug/packageDebugResources/values/values.xml" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/packaged_res/debug/packageDebugResources/values/values.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/processed_res/debug/processDebugResources/out/resources-debug.ap_" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/processed_res/debug/processDebugResources/out/resources-debug.ap_" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/43264369ce214a5e0d13483a9f643a0375c84c59b3a9814e4d120673dcfea974_0.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/43264369ce214a5e0d13483a9f643a0375c84c59b3a9814e4d120673dcfea974_1.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/43264369ce214a5e0d13483a9f643a0375c84c59b3a9814e4d120673dcfea974_2.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/43264369ce214a5e0d13483a9f643a0375c84c59b3a9814e4d120673dcfea974_3.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ComposableSingletons$ProjectDetailsScreenKt$lambda-10$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ComposableSingletons$ProjectDetailsScreenKt$lambda-10$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ComposableSingletons$ProjectDetailsScreenKt$lambda-11$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ComposableSingletons$ProjectDetailsScreenKt$lambda-11$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ComposableSingletons$ProjectDetailsScreenKt$lambda-12$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ComposableSingletons$ProjectDetailsScreenKt$lambda-12$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ComposableSingletons$ProjectDetailsScreenKt$lambda-2$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ComposableSingletons$ProjectDetailsScreenKt$lambda-2$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ComposableSingletons$ProjectDetailsScreenKt$lambda-3$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ComposableSingletons$ProjectDetailsScreenKt$lambda-3$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ComposableSingletons$ProjectDetailsScreenKt$lambda-4$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ComposableSingletons$ProjectDetailsScreenKt$lambda-4$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ComposableSingletons$ProjectDetailsScreenKt$lambda-5$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ComposableSingletons$ProjectDetailsScreenKt$lambda-5$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ComposableSingletons$ProjectDetailsScreenKt$lambda-6$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ComposableSingletons$ProjectDetailsScreenKt$lambda-6$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ComposableSingletons$ProjectDetailsScreenKt$lambda-7$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ComposableSingletons$ProjectDetailsScreenKt$lambda-7$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ComposableSingletons$ProjectDetailsScreenKt$lambda-8$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ComposableSingletons$ProjectDetailsScreenKt$lambda-8$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ComposableSingletons$ProjectDetailsScreenKt$lambda-9$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ComposableSingletons$ProjectDetailsScreenKt$lambda-9$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ComposableSingletons$ProjectDetailsScreenKt.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ComposableSingletons$ProjectDetailsScreenKt.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/Manaknight/databinding/BottomSelectCustomerBinding.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/Manaknight/databinding/BottomSelectCustomerBinding.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/Manaknight/databinding/FragmentCreateEstimationBinding.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/Manaknight/databinding/FragmentCreateEstimationBinding.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$BottomSheetContent$1$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$BottomSheetContent$1$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$BottomSheetContent$1$2$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$BottomSheetContent$1$2$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$DrawItem$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$DrawItem$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$JobDetailsSheetContentBody$1$2.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$JobDetailsSheetContentBody$1$2.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$ProjectDetailsBottomSheetContentBody$1$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$ProjectDetailsBottomSheetContentBody$1$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$ProjectDetailsBottomSheetContentBody$1$2$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$ProjectDetailsBottomSheetContentBody$1$2$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$1$1$2$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$1$1$2$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$1$1$3$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$1$1$3$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$1$1$4$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$1$1$4$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$1$1$4$1$2$1$1$1$4$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$1$1$4$1$2$1$1$1$4$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$1$1$4$1$2.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$1$1$4$1$2.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$1$1$4$1$3.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$1$1$4$1$3.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$2$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$2$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$3$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$3$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$3.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$3.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$4.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$4.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$5$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$5$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$6$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$6$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$6.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$6.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$7$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$7$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$7.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$7.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/data/local/AppPreferences.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/data/local/AppPreferences.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/data/local/AppPreferencesKt$prefModule$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/data/local/AppPreferencesKt$prefModule$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/model/remote/profitPro/AllLineItemsResponseModel.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/model/remote/profitPro/AllLineItemsResponseModel.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/model/remote/profitPro/ClientDetailRespModel.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/model/remote/profitPro/ClientDetailRespModel.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/model/remote/profitPro/DrawInfoModelRespModel.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/model/remote/profitPro/DrawInfoModelRespModel.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/model/remote/profitPro/DrawInfoRespModel.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/model/remote/profitPro/DrawInfoRespModel.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/model/remote/profitPro/DrawsRespModel.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/model/remote/profitPro/DrawsRespModel.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/model/remote/profitPro/JobDetailsRespModel.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/model/remote/profitPro/JobDetailsRespModel.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/model/remote/profitPro/MaterialRespModel.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/model/remote/profitPro/MaterialRespModel.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/model/remote/profitPro/ProjectRespModel.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/model/remote/profitPro/ProjectRespModel.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/model/remote/profitPro/TotalRespModel.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/model/remote/profitPro/TotalRespModel.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/network/RetrofitApiClientKt.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/network/RetrofitApiClientKt.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/AccountFragment.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/AccountFragment.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/CompanySetupFragment.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/CompanySetupFragment.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/CompleteSetupFragment.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/CompleteSetupFragment.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/CreateEstimationFragment$createNewEstimation$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/CreateEstimationFragment$createNewEstimation$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/CreateEstimationFragment$getCustomerList$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/CreateEstimationFragment$getCustomerList$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/CreateEstimationFragment$showCustomerSelection$lambda$16$$inlined$doAfterTextChanged$1.dex" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/CreateEstimationFragment.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/CreateEstimationFragment.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/DashboardviewFragment.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/DashboardviewFragment.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/HomeFragment.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/HomeFragment.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/LinealSetupFragment.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/LinealSetupFragment.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/MaterialSetupFragment.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/MaterialSetupFragment.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/ProfieViewFragment.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/ProfieViewFragment.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/ProjectsFragment$onCreateView$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/ProjectsFragment$onCreateView$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/SquareSetupFragment.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/SquareSetupFragment.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/SubscriptionFragment.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/SubscriptionFragment.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/adapter/SimpleChatAdapter$TypeImageReceive.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/adapter/SimpleChatAdapter$TypeImageReceive.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/adapter/SimpleChatAdapter$TypeImageSend.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/adapter/SimpleChatAdapter$TypeImageSend.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/adapter/SimpleChatAdapter$TypeTextReceive.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/adapter/SimpleChatAdapter$TypeTextReceive.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/adapter/SimpleChatAdapter$TypeTextSend.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/adapter/SimpleChatAdapter$TypeTextSend.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/adapter/SimpleChatAdapter$TypeVideoReceive.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/adapter/SimpleChatAdapter$TypeVideoReceive.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/adapter/SimpleChatAdapter$TypeVideoSend.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/adapter/SimpleChatAdapter$TypeVideoSend.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/adapters/DashboardProjectsAdapter$ProjectViewHolder.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/adapters/DashboardProjectsAdapter$ProjectViewHolder.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/adapters/MonthFilterAdapter$MonthViewHolder.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/adapters/MonthFilterAdapter$MonthViewHolder.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/adapters/MultiSelectStatusFilterAdapter$StatusViewHolder.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/adapters/MultiSelectStatusFilterAdapter$StatusViewHolder.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/adapters/StatusFilterAdapter$StatusViewHolder.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/adapters/StatusFilterAdapter$StatusViewHolder.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/AddEmployeeFragment.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/AddEmployeeFragment.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/ForgetPasswordFragment.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/ForgetPasswordFragment.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/LoginFragment.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/LoginFragment.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/PreviewProjectDetailsFragment$onCreateView$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/PreviewProjectDetailsFragment$onCreateView$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/ProfileEditFragment.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/ProfileEditFragment.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/ProfileFragment.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/ProfileFragment.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/ProjectDetailsFragment$onCreateView$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/ProjectDetailsFragment$onCreateView$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/ProjectTrackingFragment$onCreateView$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/ProjectTrackingFragment$onCreateView$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/ResetPasswordFragment.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/ResetPasswordFragment.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/ResponsiveAddEmployeeFragment.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/ResponsiveAddEmployeeFragment.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/SignUpFragment.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/SignUpFragment.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/SplashFragment.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/SplashFragment.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/accountview/PlanAndBillingFragment$onCreateView$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/accountview/PlanAndBillingFragment$onCreateView$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$InvoiceScreenKt$lambda-2$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$InvoiceScreenKt$lambda-2$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$PreviewProjectDetailScreenKt$lambda-1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$PreviewProjectDetailScreenKt$lambda-1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$PreviewProjectDetailScreenKt$lambda-2$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$PreviewProjectDetailScreenKt$lambda-2$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$PreviewProjectDetailScreenKt$lambda-3$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$PreviewProjectDetailScreenKt$lambda-3$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$PreviewProjectDetailScreenKt$lambda-4$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$PreviewProjectDetailScreenKt$lambda-4$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$PreviewProjectDetailScreenKt.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$PreviewProjectDetailScreenKt.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-10$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-10$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-11$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-11$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-12$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-12$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-13$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-13$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-14$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-14$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-15$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-15$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-16$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-16$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-17$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-17$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-18$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-18$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-19$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-19$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-2$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-2$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-20$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-20$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-21$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-21$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-22$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-22$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-23$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-23$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-24$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-24$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-25$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-25$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-26$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-26$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-27$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-27$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-28$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-28$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-29$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-29$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-3$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-3$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-30$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-30$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-31$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-31$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-32$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-32$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-33$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-33$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-34$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-34$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-35$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-35$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-4$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-4$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-5$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-5$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-6$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-6$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-7$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-7$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-8$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-8$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-9$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-9$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/CostsScreenKt.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/CostsScreenKt.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/InvoiceScreenKt$ClientDetails$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/InvoiceScreenKt$ClientDetails$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/InvoiceScreenKt$InvoiceContent$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/InvoiceScreenKt$InvoiceContent$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/InvoiceScreenKt$InvoiceDescription$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/InvoiceScreenKt$InvoiceDescription$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/InvoiceScreenKt$TotalAmount$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/InvoiceScreenKt$TotalAmount$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/InvoiceScreenKt.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/InvoiceScreenKt.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/JobDetailsUIModel.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/JobDetailsUIModel.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/Material.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/Material.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PlanAndBillingScreenKt.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PlanAndBillingScreenKt.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$CheckboxWithLabel$1$1$1$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$CheckboxWithLabel$1$1$1$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$CheckboxWithLabel$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$CheckboxWithLabel$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ClientDetailsCard$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ClientDetailsCard$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ClientDetailsCard$2.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ClientDetailsCard$2.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$JobDetailCard$1$1$1$2$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$JobDetailCard$1$1$1$2$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$JobDetailCard$1$1$1$3.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$JobDetailCard$1$1$1$3.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$JobDetailCard$1$1$2$1$formattedEstimate$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$JobDetailCard$1$1$2$1$formattedEstimate$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$JobDetailCard$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$JobDetailCard$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$PaymentStagesCard$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$PaymentStagesCard$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1$1$1$1$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1$1$1$1$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1$1$1$1$1$2.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1$1$1$1$1$2.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1$1$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1$1$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1$1$1$2.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1$1$1$2.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1$1$1$3.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1$1$1$3.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1$1$1$5.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1$1$1$5.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1$1$1$6.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1$1$1$6.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1$1$1$7$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1$1$1$7$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1$1$1$7.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1$1$1$7.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1$1$1$8$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1$1$1$8$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1$1$1$8$2$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1$1$1$8$2$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1$1$1$8.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1$1$1$8.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1$1$1$invoke$$inlined$itemsIndexed$default$3.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1$1$1$invoke$$inlined$itemsIndexed$default$3.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailMainScreen$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailMainScreen$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailMainScreen$2.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailMainScreen$2.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectTotalsCard$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectTotalsCard$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$SendEstimateButton$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$SendEstimateButton$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddMaterialExpenseSheetHeader$1$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddMaterialExpenseSheetHeader$1$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddPaymentMethodSheetContent$1$4$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddPaymentMethodSheetContent$1$4$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddPaymentMethodSheetContent$1$5$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddPaymentMethodSheetContent$1$5$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddPaymentMethodSheetHeader$1$2.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddPaymentMethodSheetHeader$1$2.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$DrawsDropdownFilter$2.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$DrawsDropdownFilter$2.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$DrawsStatusFilterDropdown$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$DrawsStatusFilterDropdown$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$EditLaborSheetHeader$1$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$EditLaborSheetHeader$1$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$EditMaterialExpenseSheetHeader$1$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$EditMaterialExpenseSheetHeader$1$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$LaborContent$2$2$2$invoke$$inlined$items$default$4.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$LaborContent$2$2$2$invoke$$inlined$items$default$4.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$LaborContent$2$2$2.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$LaborContent$2$2$2.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$LaborContent$2$4.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$LaborContent$2$4.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$LaborContent$2$5.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$LaborContent$2$5.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$2$2$invoke$$inlined$items$default$4.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$2$2$invoke$$inlined$items$default$4.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$2$2.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$2$2.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$4.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$4.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$5.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$5.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$10.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$10.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$11.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$11.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$13.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$13.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$14.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$14.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$4.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$4.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$5.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$5.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$7.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$7.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$8.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$8.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$TabButton$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$TabButton$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectsScreen$2.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectsScreen$2.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/TeamScreenKt.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/TeamScreenKt.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/utils/DynamicLineItemManager.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/utils/DynamicLineItemManager.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/utils/ResponsiveBaseDialogFragment.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/utils/ResponsiveBaseDialogFragment.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/utils/ResponsiveBottomSheetHelper$Companion.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/utils/ResponsiveBottomSheetHelper$Companion.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/utils/SubscriptionManager.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/utils/SubscriptionManager.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/widget/SimpleChatView.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/widget/SimpleChatView.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/runtime_symbol_list/debug/processDebugResources/R.txt" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/runtime_symbol_list/debug/processDebugResources/R.txt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/stable_resource_ids_file/debug/processDebugResources/stableIds.txt" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/stable_resource_ids_file/debug/processDebugResources/stableIds.txt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/symbol_list_with_package_name/debug/processDebugResources/package-aware-r.txt" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/symbol_list_with_package_name/debug/processDebugResources/package-aware-r.txt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.values" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.values" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.values.s" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.values.s" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.keystream" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.keystream" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.keystream.len" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.keystream.len" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.len" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.len" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab_i" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab_i" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream.len" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream.len" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.len" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.len" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.values" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.values" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.values.s" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.values.s" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/constants.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/constants.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/constants.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/constants.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/inline-functions.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/inline-functions.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/inline-functions.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/inline-functions.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream.len" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream.len" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.values" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.values" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.values.s" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.values.s" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/java-sources-proto-map.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/java-sources-proto-map.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/java-sources-proto-map.tab.values" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/java-sources-proto-map.tab.values" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/java-sources-proto-map.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/java-sources-proto-map.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/java-sources-proto-map.tab.values.s" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/java-sources-proto-map.tab.values.s" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.keystream" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.keystream" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.keystream.len" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.keystream.len" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.len" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.len" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.values" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.values" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.values.s" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.values.s" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab_i" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab_i" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.values" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.values" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.values.s" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.values.s" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/counters.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/counters.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.keystream" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.keystream" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.keystream.len" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.keystream.len" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.len" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.len" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab_i" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab_i" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.keystream" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.keystream" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.keystream.len" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.keystream.len" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.len" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.len" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.values" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.values" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.values.s" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.values.s" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab_i" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab_i" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/last-build.bin" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/last-build.bin" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/classpath-snapshot/shrunk-classpath-snapshot.bin" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/classpath-snapshot/shrunk-classpath-snapshot.bin" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/local-state/build-history.bin" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/local-state/build-history.bin" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.keystream" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.keystream" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.values" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.values" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.values.s" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.values.s" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab_i" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab_i" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.keystream" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.keystream" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.keystream.len" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.keystream.len" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.len" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.len" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab_i" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab_i" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream.len" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream.len" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.len" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.len" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.values" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.values" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.values.s" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.values.s" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream.len" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream.len" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.len" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.len" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.values" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.values" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.values.s" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.values.s" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/java-sources-proto-map.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/java-sources-proto-map.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/java-sources-proto-map.tab.keystream" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/java-sources-proto-map.tab.keystream" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/java-sources-proto-map.tab.keystream.len" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/java-sources-proto-map.tab.keystream.len" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/java-sources-proto-map.tab.len" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/java-sources-proto-map.tab.len" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/java-sources-proto-map.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/java-sources-proto-map.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/java-sources-proto-map.tab_i" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/java-sources-proto-map.tab_i" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.keystream" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.keystream" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.keystream.len" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.keystream.len" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.len" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.len" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.values" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.values" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.values.s" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.values.s" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab_i" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab_i" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream.len" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream.len" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.len" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.len" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.values" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.values" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.values.s" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.values.s" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab_i" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab_i" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.keystream" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.keystream" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.keystream.len" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.keystream.len" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.len" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.len" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab_i" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab_i" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/counters.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/counters.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.keystream" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.keystream" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab_i" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab_i" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.keystream" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.keystream" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.keystream.len" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.keystream.len" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.len" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.len" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.values" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.values" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.values.s" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.values.s" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab_i" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab_i" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.keystream" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.keystream" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.keystream.len" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.keystream.len" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.len" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.len" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.values" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.values" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.values.s" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.values.s" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab_i" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab_i" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/last-build.bin" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/last-build.bin" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/local-state/build-history.bin" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/local-state/build-history.bin" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kapt3/incApCache/debug/classpath-structure.bin" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kapt3/incApCache/debug/classpath-structure.bin" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kapt3/incApCache/debug/java-cache.bin" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kapt3/incApCache/debug/java-cache.bin" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kapt3/incrementalData/debug/com/manaknight/app/data/local/AppPreferences.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kapt3/incrementalData/debug/com/manaknight/app/data/local/AppPreferences.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kapt3/incrementalData/debug/com/manaknight/app/model/remote/profitPro/AllLineItemsResponseModel.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kapt3/incrementalData/debug/com/manaknight/app/model/remote/profitPro/AllLineItemsResponseModel.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kapt3/incrementalData/debug/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kapt3/incrementalData/debug/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kapt3/incrementalData/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kapt3/incrementalData/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/ProjectDetailsScreenKt.kapt_metadata" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/ProjectDetailsScreenKt.kapt_metadata" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/adapter/MaterialLineAdapter.kapt_metadata" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/adapter/MaterialLineAdapter.kapt_metadata" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/data/local/AppPreferences.java" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/data/local/AppPreferences.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/data/local/AppPreferences.kapt_metadata" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/data/local/AppPreferences.kapt_metadata" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/model/remote/profitPro/AllLineItemsResponseModel.java" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/model/remote/profitPro/AllLineItemsResponseModel.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/model/remote/profitPro/AllLineItemsResponseModel.kapt_metadata" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/model/remote/profitPro/AllLineItemsResponseModel.kapt_metadata" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/model/remote/profitPro/ClientDetailRespModel.kapt_metadata" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/model/remote/profitPro/ClientDetailRespModel.kapt_metadata" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/model/remote/profitPro/DrawInfoModelRespModel.kapt_metadata" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/model/remote/profitPro/DrawInfoModelRespModel.kapt_metadata" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/model/remote/profitPro/DrawInfoRespModel.kapt_metadata" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/model/remote/profitPro/DrawInfoRespModel.kapt_metadata" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/model/remote/profitPro/DrawsRespModel.kapt_metadata" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/model/remote/profitPro/DrawsRespModel.kapt_metadata" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/model/remote/profitPro/JobDetailsRespModel.kapt_metadata" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/model/remote/profitPro/JobDetailsRespModel.kapt_metadata" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/model/remote/profitPro/MaterialRespModel.kapt_metadata" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/model/remote/profitPro/MaterialRespModel.kapt_metadata" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/model/remote/profitPro/ProjectRespModel.kapt_metadata" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/model/remote/profitPro/ProjectRespModel.kapt_metadata" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/model/remote/profitPro/TotalRespModel.kapt_metadata" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/model/remote/profitPro/TotalRespModel.kapt_metadata" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/ui/CreateEstimationFragment.kapt_metadata" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/ui/CreateEstimationFragment.kapt_metadata" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/ui/screens/InvoiceScreenKt.kapt_metadata" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/ui/screens/InvoiceScreenKt.kapt_metadata" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/ui/screens/Material.kapt_metadata" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/ui/screens/Material.kapt_metadata" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt.java" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt.kapt_metadata" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt.kapt_metadata" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt.kapt_metadata" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt.kapt_metadata" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ComposableSingletons$ProjectDetailsScreenKt$lambda-10$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ComposableSingletons$ProjectDetailsScreenKt$lambda-10$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ComposableSingletons$ProjectDetailsScreenKt$lambda-11$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ComposableSingletons$ProjectDetailsScreenKt$lambda-11$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ComposableSingletons$ProjectDetailsScreenKt$lambda-12$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ComposableSingletons$ProjectDetailsScreenKt$lambda-12$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ComposableSingletons$ProjectDetailsScreenKt$lambda-2$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ComposableSingletons$ProjectDetailsScreenKt$lambda-2$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ComposableSingletons$ProjectDetailsScreenKt$lambda-3$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ComposableSingletons$ProjectDetailsScreenKt$lambda-3$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ComposableSingletons$ProjectDetailsScreenKt$lambda-4$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ComposableSingletons$ProjectDetailsScreenKt$lambda-4$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ComposableSingletons$ProjectDetailsScreenKt$lambda-5$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ComposableSingletons$ProjectDetailsScreenKt$lambda-5$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ComposableSingletons$ProjectDetailsScreenKt$lambda-6$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ComposableSingletons$ProjectDetailsScreenKt$lambda-6$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ComposableSingletons$ProjectDetailsScreenKt$lambda-7$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ComposableSingletons$ProjectDetailsScreenKt$lambda-7$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ComposableSingletons$ProjectDetailsScreenKt$lambda-8$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ComposableSingletons$ProjectDetailsScreenKt$lambda-8$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ComposableSingletons$ProjectDetailsScreenKt$lambda-9$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ComposableSingletons$ProjectDetailsScreenKt$lambda-9$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ComposableSingletons$ProjectDetailsScreenKt.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ComposableSingletons$ProjectDetailsScreenKt.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$BottomSheetContent$1$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$BottomSheetContent$1$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$BottomSheetContent$1$2$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$BottomSheetContent$1$2$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$DrawItem$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$DrawItem$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$JobDetailsSheetContentBody$1$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$JobDetailsSheetContentBody$1$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$ProjectDetailsBottomSheetContentBody$1$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$ProjectDetailsBottomSheetContentBody$1$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$ProjectDetailsBottomSheetContentBody$1$2$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$ProjectDetailsBottomSheetContentBody$1$2$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$1$1$2$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$1$1$2$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$1$1$3$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$1$1$3$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$1$1$4$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$1$1$4$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$1$1$4$1$2$1$1$1$4$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$1$1$4$1$2$1$1$1$4$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$1$1$4$1$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$1$1$4$1$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$1$1$4$1$3.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$1$1$4$1$3.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$2$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$2$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$3$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$3$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$3.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$3.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$4.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$4.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$5$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$5$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$6$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$6$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$6.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$6.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$7$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$7$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$7.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$7.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/data/local/AppPreferences.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/data/local/AppPreferences.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/data/local/AppPreferencesKt$prefModule$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/data/local/AppPreferencesKt$prefModule$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/model/remote/profitPro/AllLineItemsResponseModel.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/model/remote/profitPro/AllLineItemsResponseModel.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/model/remote/profitPro/ClientDetailRespModel.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/model/remote/profitPro/ClientDetailRespModel.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/model/remote/profitPro/DrawInfoModelRespModel.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/model/remote/profitPro/DrawInfoModelRespModel.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/model/remote/profitPro/DrawInfoRespModel.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/model/remote/profitPro/DrawInfoRespModel.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/model/remote/profitPro/DrawsRespModel.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/model/remote/profitPro/DrawsRespModel.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/model/remote/profitPro/JobDetailsRespModel.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/model/remote/profitPro/JobDetailsRespModel.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/model/remote/profitPro/MaterialRespModel.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/model/remote/profitPro/MaterialRespModel.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/model/remote/profitPro/ProjectRespModel.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/model/remote/profitPro/ProjectRespModel.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/model/remote/profitPro/TotalRespModel.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/model/remote/profitPro/TotalRespModel.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/CreateEstimationFragment$createNewEstimation$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/CreateEstimationFragment$createNewEstimation$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/CreateEstimationFragment$getCustomerList$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/CreateEstimationFragment$getCustomerList$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/CreateEstimationFragment$showCustomerSelection$lambda$16$$inlined$doAfterTextChanged$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/CreateEstimationFragment.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/CreateEstimationFragment.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/ProjectsFragment$onCreateView$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/ProjectsFragment$onCreateView$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/PreviewProjectDetailsFragment$onCreateView$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/PreviewProjectDetailsFragment$onCreateView$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/ProjectDetailsFragment$onCreateView$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/ProjectDetailsFragment$onCreateView$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/ProjectTrackingFragment$onCreateView$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/ProjectTrackingFragment$onCreateView$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/accountview/PlanAndBillingFragment$onCreateView$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/accountview/PlanAndBillingFragment$onCreateView$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$InvoiceScreenKt$lambda-2$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$InvoiceScreenKt$lambda-2$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$PreviewProjectDetailScreenKt$lambda-1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$PreviewProjectDetailScreenKt$lambda-1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$PreviewProjectDetailScreenKt$lambda-2$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$PreviewProjectDetailScreenKt$lambda-2$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$PreviewProjectDetailScreenKt$lambda-3$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$PreviewProjectDetailScreenKt$lambda-3$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$PreviewProjectDetailScreenKt$lambda-4$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$PreviewProjectDetailScreenKt$lambda-4$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$PreviewProjectDetailScreenKt.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$PreviewProjectDetailScreenKt.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-10$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-10$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-11$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-11$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-12$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-12$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-13$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-13$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-14$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-14$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-15$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-15$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-16$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-16$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-17$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-17$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-18$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-18$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-19$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-19$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-2$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-2$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-20$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-20$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-21$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-21$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-22$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-22$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-23$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-23$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-24$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-24$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-25$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-25$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-26$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-26$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-27$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-27$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-28$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-28$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-29$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-29$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-3$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-3$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-30$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-30$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-31$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-31$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-32$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-32$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-33$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-33$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-34$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-34$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-35$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-35$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-4$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-4$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-5$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-5$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-6$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-6$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-7$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-7$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-8$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-8$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-9$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-9$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/CostsScreenKt.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/CostsScreenKt.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/InvoiceScreenKt$ClientDetails$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/InvoiceScreenKt$ClientDetails$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/InvoiceScreenKt$InvoiceContent$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/InvoiceScreenKt$InvoiceContent$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/InvoiceScreenKt$InvoiceDescription$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/InvoiceScreenKt$InvoiceDescription$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/InvoiceScreenKt$TotalAmount$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/InvoiceScreenKt$TotalAmount$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/InvoiceScreenKt.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/InvoiceScreenKt.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/JobDetailsUIModel.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/JobDetailsUIModel.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/Material.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/Material.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PlanAndBillingScreenKt.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PlanAndBillingScreenKt.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$CheckboxWithLabel$1$1$1$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$CheckboxWithLabel$1$1$1$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$CheckboxWithLabel$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$CheckboxWithLabel$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ClientDetailsCard$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ClientDetailsCard$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ClientDetailsCard$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ClientDetailsCard$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$JobDetailCard$1$1$1$2$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$JobDetailCard$1$1$1$2$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$JobDetailCard$1$1$1$3.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$JobDetailCard$1$1$1$3.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$JobDetailCard$1$1$2$1$formattedEstimate$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$JobDetailCard$1$1$2$1$formattedEstimate$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$JobDetailCard$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$JobDetailCard$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$PaymentStagesCard$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$PaymentStagesCard$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1$1$1$1$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1$1$1$1$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1$1$1$1$1$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1$1$1$1$1$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1$1$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1$1$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1$1$1$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1$1$1$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1$1$1$3.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1$1$1$3.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1$1$1$5.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1$1$1$5.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1$1$1$6.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1$1$1$6.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1$1$1$7$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1$1$1$7$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1$1$1$7.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1$1$1$7.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1$1$1$8$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1$1$1$8$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1$1$1$8$2$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1$1$1$8$2$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1$1$1$8.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1$1$1$8.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1$1$1$invoke$$inlined$itemsIndexed$default$3.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1$1$1$invoke$$inlined$itemsIndexed$default$3.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailContent$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailMainScreen$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailMainScreen$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailMainScreen$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectDetailMainScreen$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectTotalsCard$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$ProjectTotalsCard$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$SendEstimateButton$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt$SendEstimateButton$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/PreviewProjectDetailScreenKt.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddMaterialExpenseSheetHeader$1$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddMaterialExpenseSheetHeader$1$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddPaymentMethodSheetContent$1$4$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddPaymentMethodSheetContent$1$4$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddPaymentMethodSheetContent$1$5$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddPaymentMethodSheetContent$1$5$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddPaymentMethodSheetHeader$1$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddPaymentMethodSheetHeader$1$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$DrawsDropdownFilter$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$DrawsDropdownFilter$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$DrawsStatusFilterDropdown$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$DrawsStatusFilterDropdown$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$EditLaborSheetHeader$1$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$EditLaborSheetHeader$1$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$EditMaterialExpenseSheetHeader$1$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$EditMaterialExpenseSheetHeader$1$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$LaborContent$2$2$2$invoke$$inlined$items$default$4.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$LaborContent$2$2$2$invoke$$inlined$items$default$4.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$LaborContent$2$2$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$LaborContent$2$2$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$LaborContent$2$4.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$LaborContent$2$4.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$LaborContent$2$5.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$LaborContent$2$5.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$2$2$invoke$$inlined$items$default$4.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$2$2$invoke$$inlined$items$default$4.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$2$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$2$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$4.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$4.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$5.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$5.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$10.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$10.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$11.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$11.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$13.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$13.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$14.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$14.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$4.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$4.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$5.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$5.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$7.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$7.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$8.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$8.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$TabButton$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$TabButton$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectsScreen$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectsScreen$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/TeamScreenKt.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/TeamScreenKt.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/manaknight/app/adapters/MaterialLineAdapter.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/manaknight/app/adapters/MaterialLineAdapter.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/manaknight/app/data/local/AppPreferences.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/manaknight/app/data/local/AppPreferences.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/manaknight/app/model/remote/profitPro/AllLineItemsResponseModel.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/manaknight/app/model/remote/profitPro/AllLineItemsResponseModel.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/manaknight/app/ui/components/CustomCheckbox.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/manaknight/app/ui/components/CustomCheckbox.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/manaknight/app/ui/components/ResponsiveSheetContainer.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/manaknight/app/ui/components/ResponsiveSheetContainer.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/manaknight/app/ui/fragments/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/manaknight/app/ui/fragments/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/manaknight/app/ui/screens/InvoiceScreen.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/manaknight/app/ui/screens/InvoiceScreen.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/manaknight/app/ui/screens/PreviewProjectDetailScreen.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/manaknight/app/ui/screens/PreviewProjectDetailScreen.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/manaknight/app/ui/screens/ProjectDetailsScreen.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/manaknight/app/ui/screens/ProjectDetailsScreen.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/manaknight/app/ui/screens/ProjectTrackingScreen.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/manaknight/app/ui/screens/ProjectTrackingScreen.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/res/drawable/custom_checkbox.xml" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/res/drawable/custom_checkbox.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/res/drawable/custom_checkbox_selector.xml" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/res/drawable/custom_checkbox_selector.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/res/layout-sw600dp/fragment_home.xml" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/res/layout-sw600dp/fragment_home.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/res/layout/bottom_select_customer.xml" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/res/layout/bottom_select_customer.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/res/layout/fragment_add_line_items.xml" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/res/layout/fragment_add_line_items.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/res/layout/fragment_create_customer.xml" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/res/layout/fragment_create_customer.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/res/layout/fragment_create_estimation.xml" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/res/layout/fragment_create_estimation.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/res/layout/fragment_home.xml" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/res/layout/fragment_home.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/res/layout/fragment_material_line_item.xml" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/res/layout/fragment_material_line_item.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/res/layout/item_line_material.xml" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/res/layout/item_line_material.xml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ClangdSettings">
    <option name="formatViaClangd" value="false" />
  </component>
  <component name="ExecutionTargetManager" SELECTED_TARGET="device_and_snapshot_combo_box_target[DeviceId(pluginId=LocalEmulator, isTemplate=false, identifier=path=C:\Users\<USER>\.android\avd\Pixel_5_API_36.avd)]" />
  <component name="ExternalProjectsData">
    <projectState path="$PROJECT_DIR$">
      <ProjectState />
    </projectState>
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="resourceFile" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="2yvLLqUWRHbky1ztneXbIyjFVNC" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Android App.app.executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.cidr.known.project.marker&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.readMode.enableVisualFormatting&quot;: &quot;true&quot;,
    &quot;cf.first.check.clang-format&quot;: &quot;false&quot;,
    &quot;cidr.known.project.marker&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;faceid&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/AndroidStudioProjects/luv_android&quot;,
    &quot;project.structure.last.edited&quot;: &quot;SDK Location&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.17&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;AndroidSdkUpdater&quot;
  }
}</component>
  <component name="PsdUISettings">
    <option name="LAST_EDITED_BUILD_TYPE" value="release" />
  </component>
  <component name="RecentsManager">
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable" />
    </key>
    <key name="CopyKotlinDeclarationDialog.RECENTS_KEY">
      <recent name="com.manaknight.app.model.remote" />
    </key>
  </component>
  <component name="RunManager">
    <configuration name="app" type="AndroidRunConfigurationType" factoryName="Android App" activateToolWindowBeforeRun="false">
      <module name="profitpro.app" />
      <option name="ANDROID_RUN_CONFIGURATION_SCHEMA_VERSION" value="1" />
      <option name="DEPLOY" value="true" />
      <option name="DEPLOY_APK_FROM_BUNDLE" value="false" />
      <option name="DEPLOY_AS_INSTANT" value="false" />
      <option name="ARTIFACT_NAME" value="" />
      <option name="PM_INSTALL_OPTIONS" value="" />
      <option name="ALL_USERS" value="false" />
      <option name="ALWAYS_INSTALL_WITH_PM" value="false" />
      <option name="ALLOW_ASSUME_VERIFIED" value="false" />
      <option name="CLEAR_APP_STORAGE" value="false" />
      <option name="DYNAMIC_FEATURES_DISABLED_LIST" value="" />
      <option name="ACTIVITY_EXTRA_FLAGS" value="" />
      <option name="MODE" value="default_activity" />
      <option name="RESTORE_ENABLED" value="false" />
      <option name="RESTORE_FILE" value="" />
      <option name="CLEAR_LOGCAT" value="false" />
      <option name="SHOW_LOGCAT_AUTOMATICALLY" value="false" />
      <option name="TARGET_SELECTION_MODE" value="DEVICE_AND_SNAPSHOT_COMBO_BOX" />
      <option name="SELECTED_CLOUD_MATRIX_CONFIGURATION_ID" value="-1" />
      <option name="SELECTED_CLOUD_MATRIX_PROJECT_ID" value="" />
      <option name="DEBUGGER_TYPE" value="Auto" />
      <Auto>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Auto>
      <Hybrid>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Hybrid>
      <Java>
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Java>
      <Native>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Native>
      <Profilers>
        <option name="ADVANCED_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_CONFIGURATION_NAME" value="Java/Kotlin Method Sample (legacy)" />
        <option name="STARTUP_NATIVE_MEMORY_PROFILING_ENABLED" value="false" />
        <option name="NATIVE_MEMORY_SAMPLE_RATE_BYTES" value="2048" />
      </Profilers>
      <option name="DEEP_LINK" value="" />
      <option name="ACTIVITY" value="" />
      <option name="ACTIVITY_CLASS" value="" />
      <option name="SEARCH_ACTIVITY_IN_GLOBAL_SCOPE" value="false" />
      <option name="SKIP_ACTIVITY_VALIDATION" value="false" />
      <method v="2">
        <option name="Android.Gradle.BeforeRunTask" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="7629a6d9-4a69-4249-9449-416164fda9ed" name="Changes" comment="" />
      <created>1750710046701</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1750710046701</updated>
    </task>
    <servers />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="kotlin-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/manaknight/app/ui/screens/ProjectTrackingScreen.kt</url>
          <line>995</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="kotlin-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/manaknight/app/ui/screens/ProjectDetailsScreen.kt</url>
          <line>679</line>
          <option name="timeStamp" value="3" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="kotlin-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/manaknight/app/ui/screens/ProjectTrackingScreen.kt</url>
          <line>776</line>
          <option name="timeStamp" value="6" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="kotlin-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/manaknight/app/ui/screens/ProjectTrackingScreen.kt</url>
          <line>1245</line>
          <option name="timeStamp" value="7" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="kotlin-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/manaknight/app/ui/screens/ProjectDetailsScreen.kt</url>
          <line>527</line>
          <option name="timeStamp" value="10" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="kotlin-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/manaknight/app/ui/screens/ProjectDetailsScreen.kt</url>
          <line>264</line>
          <option name="timeStamp" value="11" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="kotlin-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/manaknight/app/adapters/DrawsAdapter.kt</url>
          <line>60</line>
          <option name="timeStamp" value="14" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="compose-function">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/manaknight/app/ui/screens/ProjectTrackingScreen.kt</url>
          <line>686</line>
          <properties>
            <option name="EMULATED" value="true" />
            <option name="WATCH_EXIT" value="false" />
          </properties>
          <option name="timeStamp" value="5" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="kotlin-function">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/manaknight/app/network/ApiService.kt</url>
          <line>996</line>
          <properties class="com.manaknight.app.network.ApiService" method="getDrawsList">
            <option name="EMULATED" value="true" />
            <option name="WATCH_EXIT" value="false" />
          </properties>
          <option name="timeStamp" value="9" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="play_dynamic_filters_status">
    <option name="appIdToCheckInfo">
      <map>
        <entry key="com.manaknight.app">
          <value>
            <CheckInfo lastCheckTimestamp="1753981877355" />
          </value>
        </entry>
        <entry key="com.manaknight.app.test">
          <value>
            <CheckInfo lastCheckTimestamp="1753981877353" />
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>