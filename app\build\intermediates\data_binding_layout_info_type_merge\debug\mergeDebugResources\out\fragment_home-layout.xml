<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_home" modulePackage="Manaknight" filePath="app\src\main\res\layout\fragment_home.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/fragment_home_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="662" endOffset="51"/></Target><Target tag="binding_1" view="LinearLayout"><Expressions/><location startLine="6" startOffset="4" endLine="660" endOffset="18"/></Target><Target id="@+id/headerInclude" tag="binding_1" include="header"><Expressions/><location startLine="13" startOffset="8" endLine="15" endOffset="37"/></Target><Target id="@+id/txtTotalProfitOverhead" view="TextView"><Expressions/><location startLine="79" startOffset="36" endLine="93" endOffset="75"/></Target><Target id="@+id/txtCompanyHealth" view="TextView"><Expressions/><location startLine="139" startOffset="36" endLine="153" endOffset="75"/></Target><Target id="@+id/btnViewDetails" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="166" startOffset="16" endLine="182" endOffset="49"/></Target><Target id="@+id/txtTotalContracts" view="TextView"><Expressions/><location startLine="211" startOffset="28" endLine="225" endOffset="67"/></Target><Target id="@+id/txtTotalAR" view="TextView"><Expressions/><location startLine="247" startOffset="28" endLine="262" endOffset="67"/></Target><Target id="@+id/txtMaterialBalance" view="TextView"><Expressions/><location startLine="294" startOffset="28" endLine="309" endOffset="67"/></Target><Target id="@+id/txtMaterialSpent" view="TextView"><Expressions/><location startLine="331" startOffset="28" endLine="346" endOffset="67"/></Target><Target id="@+id/txtLaborBalance" view="TextView"><Expressions/><location startLine="377" startOffset="28" endLine="392" endOffset="67"/></Target><Target id="@+id/txtLaborSpent" view="TextView"><Expressions/><location startLine="414" startOffset="28" endLine="429" endOffset="67"/></Target><Target id="@+id/firstRow" view="LinearLayout"><Expressions/><location startLine="455" startOffset="20" endLine="537" endOffset="34"/></Target><Target id="@+id/draftsCard" view="LinearLayout"><Expressions/><location startLine="462" startOffset="24" endLine="497" endOffset="38"/></Target><Target id="@+id/txtDraftsCount" view="TextView"><Expressions/><location startLine="471" startOffset="28" endLine="485" endOffset="67"/></Target><Target id="@+id/outstandingCard" view="LinearLayout"><Expressions/><location startLine="501" startOffset="24" endLine="536" endOffset="38"/></Target><Target id="@+id/txtOutstandingCount" view="TextView"><Expressions/><location startLine="510" startOffset="28" endLine="524" endOffset="67"/></Target><Target id="@+id/activeCard" view="LinearLayout"><Expressions/><location startLine="550" startOffset="24" endLine="585" endOffset="38"/></Target><Target id="@+id/txtActiveCount" view="TextView"><Expressions/><location startLine="559" startOffset="28" endLine="573" endOffset="67"/></Target><Target id="@+id/completedCard" view="LinearLayout"><Expressions/><location startLine="589" startOffset="24" endLine="624" endOffset="38"/></Target><Target id="@+id/txtCompletedCount" view="TextView"><Expressions/><location startLine="598" startOffset="28" endLine="612" endOffset="67"/></Target><Target id="@+id/addNewEstimation" view="ImageView"><Expressions/><location startLine="628" startOffset="20" endLine="636" endOffset="62"/></Target></Targets></Layout>