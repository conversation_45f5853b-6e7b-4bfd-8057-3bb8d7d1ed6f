<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_home" modulePackage="Manaknight" filePath="app\src\main\res\layout\fragment_home.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/fragment_home_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="661" endOffset="51"/></Target><Target tag="binding_1" view="LinearLayout"><Expressions/><location startLine="6" startOffset="4" endLine="659" endOffset="18"/></Target><Target id="@+id/headerInclude" tag="binding_1" include="header"><Expressions/><location startLine="13" startOffset="8" endLine="15" endOffset="37"/></Target><Target id="@+id/txtTotalProfitOverhead" view="TextView"><Expressions/><location startLine="79" startOffset="36" endLine="93" endOffset="75"/></Target><Target id="@+id/txtCompanyHealth" view="TextView"><Expressions/><location startLine="139" startOffset="36" endLine="153" endOffset="75"/></Target><Target id="@+id/btnViewDetails" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="166" startOffset="16" endLine="181" endOffset="49"/></Target><Target id="@+id/txtTotalContracts" view="TextView"><Expressions/><location startLine="210" startOffset="28" endLine="224" endOffset="67"/></Target><Target id="@+id/txtTotalAR" view="TextView"><Expressions/><location startLine="246" startOffset="28" endLine="261" endOffset="67"/></Target><Target id="@+id/txtMaterialBalance" view="TextView"><Expressions/><location startLine="293" startOffset="28" endLine="308" endOffset="67"/></Target><Target id="@+id/txtMaterialSpent" view="TextView"><Expressions/><location startLine="330" startOffset="28" endLine="345" endOffset="67"/></Target><Target id="@+id/txtLaborBalance" view="TextView"><Expressions/><location startLine="376" startOffset="28" endLine="391" endOffset="67"/></Target><Target id="@+id/txtLaborSpent" view="TextView"><Expressions/><location startLine="413" startOffset="28" endLine="428" endOffset="67"/></Target><Target id="@+id/firstRow" view="LinearLayout"><Expressions/><location startLine="454" startOffset="20" endLine="536" endOffset="34"/></Target><Target id="@+id/draftsCard" view="LinearLayout"><Expressions/><location startLine="461" startOffset="24" endLine="496" endOffset="38"/></Target><Target id="@+id/txtDraftsCount" view="TextView"><Expressions/><location startLine="470" startOffset="28" endLine="484" endOffset="67"/></Target><Target id="@+id/outstandingCard" view="LinearLayout"><Expressions/><location startLine="500" startOffset="24" endLine="535" endOffset="38"/></Target><Target id="@+id/txtOutstandingCount" view="TextView"><Expressions/><location startLine="509" startOffset="28" endLine="523" endOffset="67"/></Target><Target id="@+id/activeCard" view="LinearLayout"><Expressions/><location startLine="549" startOffset="24" endLine="584" endOffset="38"/></Target><Target id="@+id/txtActiveCount" view="TextView"><Expressions/><location startLine="558" startOffset="28" endLine="572" endOffset="67"/></Target><Target id="@+id/completedCard" view="LinearLayout"><Expressions/><location startLine="588" startOffset="24" endLine="623" endOffset="38"/></Target><Target id="@+id/txtCompletedCount" view="TextView"><Expressions/><location startLine="597" startOffset="28" endLine="611" endOffset="67"/></Target><Target id="@+id/addNewEstimation" view="ImageView"><Expressions/><location startLine="627" startOffset="20" endLine="635" endOffset="62"/></Target></Targets></Layout>