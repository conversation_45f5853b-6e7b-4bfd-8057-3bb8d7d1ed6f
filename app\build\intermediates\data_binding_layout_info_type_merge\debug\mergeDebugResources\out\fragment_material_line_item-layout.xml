<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_material_line_item" modulePackage="Manaknight" filePath="app\src\main\res\layout\fragment_material_line_item.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout" rootNodeViewId="@+id/coss"><Targets><Target id="@+id/coss" tag="layout/fragment_material_line_item_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="122" endOffset="51"/></Target><Target id="@+id/headerInclude" tag="layout/fragment_material_line_item_0" include="header"><Expressions/><location startLine="13" startOffset="4" endLine="17" endOffset="9"/></Target><Target id="@+id/topLayout" view="LinearLayout"><Expressions/><location startLine="19" startOffset="4" endLine="67" endOffset="18"/></Target><Target id="@+id/btnAddLineItem" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="49" startOffset="12" endLine="64" endOffset="45"/></Target><Target id="@+id/line1" view="LinearLayout"><Expressions/><location startLine="69" startOffset="4" endLine="119" endOffset="18"/></Target><Target id="@+id/txtFullName" view="TextView"><Expressions/><location startLine="82" startOffset="8" endLine="93" endOffset="70"/></Target><Target id="@+id/edTxtFullName" view="EditText"><Expressions/><location startLine="95" startOffset="8" endLine="110" endOffset="13"/></Target><Target id="@+id/linealRecylerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="112" startOffset="8" endLine="118" endOffset="53"/></Target></Targets></Layout>