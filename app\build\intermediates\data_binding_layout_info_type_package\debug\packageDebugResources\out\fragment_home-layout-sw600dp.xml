<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_home" modulePackage="Manaknight" filePath="app\src\main\res\layout-sw600dp\fragment_home.xml" directory="layout-sw600dp" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout-sw600dp/fragment_home_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="685" endOffset="51"/></Target><Target id="@+id/headerInclude" tag="layout-sw600dp/fragment_home_0" include="header"><Expressions/><location startLine="8" startOffset="4" endLine="12" endOffset="45"/></Target><Target id="@+id/container" view="RelativeLayout"><Expressions/><location startLine="14" startOffset="4" endLine="662" endOffset="20"/></Target><Target id="@+id/innerConstraintLayout" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="24" startOffset="8" endLine="661" endOffset="59"/></Target><Target id="@+id/scrollTotalProfitOverhead" view="HorizontalScrollView"><Expressions/><location startLine="93" startOffset="36" endLine="114" endOffset="58"/></Target><Target id="@+id/txtTotalProfitOverhead" view="TextView"><Expressions/><location startLine="98" startOffset="40" endLine="113" endOffset="79"/></Target><Target id="@+id/scrollCompanyHealth" view="HorizontalScrollView"><Expressions/><location startLine="153" startOffset="36" endLine="173" endOffset="58"/></Target><Target id="@+id/txtCompanyHealth" view="TextView"><Expressions/><location startLine="158" startOffset="40" endLine="172" endOffset="79"/></Target><Target id="@+id/btnViewDetails" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="184" startOffset="20" endLine="198" endOffset="53"/></Target><Target id="@+id/txtTotalContracts" view="TextView"><Expressions/><location startLine="227" startOffset="32" endLine="242" endOffset="71"/></Target><Target id="@+id/txtTotalAR" view="TextView"><Expressions/><location startLine="264" startOffset="32" endLine="279" endOffset="71"/></Target><Target id="@+id/txtMaterialBalance" view="TextView"><Expressions/><location startLine="311" startOffset="32" endLine="326" endOffset="71"/></Target><Target id="@+id/txtMaterialSpent" view="TextView"><Expressions/><location startLine="348" startOffset="32" endLine="363" endOffset="71"/></Target><Target id="@+id/txtLaborBalance" view="TextView"><Expressions/><location startLine="394" startOffset="32" endLine="409" endOffset="71"/></Target><Target id="@+id/txtLaborSpent" view="TextView"><Expressions/><location startLine="431" startOffset="32" endLine="446" endOffset="71"/></Target><Target id="@+id/firstRow" view="LinearLayout"><Expressions/><location startLine="472" startOffset="24" endLine="554" endOffset="38"/></Target><Target id="@+id/draftsCard" view="LinearLayout"><Expressions/><location startLine="479" startOffset="28" endLine="514" endOffset="42"/></Target><Target id="@+id/txtDraftsCount" view="TextView"><Expressions/><location startLine="488" startOffset="32" endLine="502" endOffset="71"/></Target><Target id="@+id/outstandingCard" view="LinearLayout"><Expressions/><location startLine="518" startOffset="28" endLine="553" endOffset="42"/></Target><Target id="@+id/txtOutstandingCount" view="TextView"><Expressions/><location startLine="527" startOffset="32" endLine="541" endOffset="71"/></Target><Target id="@+id/activeCard" view="LinearLayout"><Expressions/><location startLine="567" startOffset="28" endLine="602" endOffset="42"/></Target><Target id="@+id/txtActiveCount" view="TextView"><Expressions/><location startLine="576" startOffset="32" endLine="590" endOffset="71"/></Target><Target id="@+id/completedCard" view="LinearLayout"><Expressions/><location startLine="606" startOffset="28" endLine="641" endOffset="42"/></Target><Target id="@+id/txtCompletedCount" view="TextView"><Expressions/><location startLine="615" startOffset="32" endLine="629" endOffset="71"/></Target><Target id="@+id/addNewEstimation" view="ImageView"><Expressions/><location startLine="645" startOffset="24" endLine="653" endOffset="66"/></Target></Targets></Layout>