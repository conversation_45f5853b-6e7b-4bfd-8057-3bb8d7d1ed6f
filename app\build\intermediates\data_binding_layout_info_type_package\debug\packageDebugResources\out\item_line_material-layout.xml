<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_line_material" modulePackage="Manaknight" filePath="app\src\main\res\layout\item_line_material.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView"><Targets><Target tag="layout/item_line_material_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="128" endOffset="35"/></Target><Target id="@+id/checkBoxMaterial" view="CheckBox"><Expressions/><location startLine="21" startOffset="8" endLine="29" endOffset="54"/></Target><Target id="@+id/txtMaterialName" view="TextView"><Expressions/><location startLine="32" startOffset="8" endLine="45" endOffset="54"/></Target><Target id="@+id/txtPrice" view="TextView"><Expressions/><location startLine="47" startOffset="8" endLine="58" endOffset="70"/></Target><Target id="@+id/txtTotalPrice" view="TextView"><Expressions/><location startLine="60" startOffset="8" endLine="71" endOffset="70"/></Target><Target id="@+id/txtUnitStart" view="TextView"><Expressions/><location startLine="74" startOffset="8" endLine="85" endOffset="63"/></Target><Target id="@+id/txtUnit" view="TextView"><Expressions/><location startLine="87" startOffset="8" endLine="99" endOffset="63"/></Target><Target id="@+id/edtUnits" view="EditText"><Expressions/><location startLine="102" startOffset="8" endLine="118" endOffset="54"/></Target></Targets></Layout>