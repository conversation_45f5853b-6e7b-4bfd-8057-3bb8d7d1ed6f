<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_round_corners"
    android:orientation="vertical"
    android:layout_marginTop="90dp"

    android:paddingVertical="10dp"
    app:layout_behavior="@string/bottom_sheet_behavior">


    <LinearLayout
        android:id="@+id/header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingHorizontal="15dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:paddingTop="0dp">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_marginTop="5dp"
            android:orientation="horizontal">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"

                >

                <TextView
                    android:id="@+id/addHeading"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="New Material"
                    android:textSize="16sp"
                    android:textColor="#0A0D14"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="500"
                    android:layout_centerHorizontal="true"
                    android:layout_centerVertical="true"
                    android:gravity="center" />
            </RelativeLayout>

            <ImageView
                android:id="@+id/backButton"
                android:layout_width="25dp"
                android:layout_centerVertical="true"
                android:layout_height="25dp"
                android:src="@drawable/cross" />


            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnSave"
                android:layout_width="wrap_content"
                android:layout_height="48dp"
                android:paddingVertical="0dp"
                android:layout_alignParentEnd="true"
                android:textColor="#375DFB"
                android:textSize="16sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="500"
                app:cornerRadius="8dp"
                app:strokeWidth="2dp"
                app:strokeColor="#375DFB"
                app:backgroundTint="@color/white"
                android:text="Save" />
        </RelativeLayout>

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="10dp"
        android:background="#D3D3D3"
        />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        android:id="@+id/mainLayout"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/headerInclude">

    <LinearLayout
        android:id="@+id/line1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingHorizontal="16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/headerInclude">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingHorizontal="12dp"
            android:paddingVertical="5dp"
            android:layout_marginTop="16sp"
            android:background="@drawable/rounded_edittext"
            app:layout_constraintStart_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginTop="10dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Description"
                    android:textSize="14sp"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="500"
                    android:layout_centerVertical="true"
                    android:textColor="@color/profit_black"/>

                <EditText
                    android:id="@+id/descInput"
                    android:layout_width="match_parent"
                    android:layout_height="130dp"
                    android:background="@drawable/rounded_edittext"
                    android:padding="10dp"
                    android:textSize="14sp"

                    android:fontFamily="@font/inter"
                    android:textFontWeight="500"
                    android:gravity="top"
                    android:hint="Enter description"

                    android:layout_marginTop="8dp"/>

                <LinearLayout
                    android:id="@+id/priceDrawLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="5dp"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="15dp"
                        android:text="Draw Type"
                        android:textSize="14sp"
                        android:fontFamily="@font/inter"
                        android:textFontWeight="500"
                        android:textColor="#0A0D14"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/headerInclude" />


                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="40dp"
                            android:background="@drawable/rounded_edittext"
                            android:layout_marginTop="2dp"
                            android:layout_weight="1"
                            android:padding="6dp"
                            android:orientation="horizontal"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/linearDefaultHourlyRate"
                            >

                            <RadioButton
                                android:id="@+id/radioButtonDollar"
                                android:layout_width="25dp"
                                android:layout_height="wrap_content"
                                android:checked="true"
                                android:buttonTint="@color/brand_green"
                                app:layout_constraintTop_toTopOf="parent"/>

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="$"
                                android:textSize="14sp"
                                android:fontFamily="@font/inter"
                                android:gravity="center"
                                android:textFontWeight="400"
                                android:layout_gravity="center"
                                android:textColor="#868C98"
                                android:paddingLeft="4dp"
                                android:paddingRight="2dp"
                                />

                        </LinearLayout>

                        <View
                            android:layout_width="20dp"
                            android:layout_height="40dp"/>
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="40dp"
                            android:background="@drawable/rounded_edittext"
                            android:layout_marginTop="2dp"
                            android:layout_weight="1"
                            android:padding="6dp"
                            android:orientation="horizontal"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/linearDefaultHourlyRate"
                            >

                            <RadioButton
                                android:id="@+id/radioButtonPercentage"
                                android:layout_width="25dp"
                                android:layout_height="wrap_content"
                                android:buttonTint="@color/brand_green"
                                app:layout_constraintTop_toTopOf="parent"/>


                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="%"
                                android:textSize="14sp"
                                android:fontFamily="@font/inter"
                                android:gravity="center"
                                android:textFontWeight="400"
                                android:layout_gravity="center"
                                android:textColor="#868C98"
                                android:paddingLeft="4dp"
                                android:paddingRight="2dp"
                                />
                        </LinearLayout>
                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/priceLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="5dp"
                    android:orientation="vertical">


                    <TextView
                        android:id="@+id/tvPrice"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:text="Price"
                        android:textSize="14sp"
                        android:fontFamily="@font/inter"
                        android:textFontWeight="500"
                        android:textColor="#0A0D14"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/headerInclude" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="40dp"
                        android:background="@drawable/rounded_edittext"
                        android:layout_marginTop="2dp"
                        android:padding="6dp"
                        android:orientation="horizontal"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/linearDefaultHourlyRate"
                        >

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="$"
                            android:textSize="14sp"
                            android:fontFamily="@font/inter"
                            android:gravity="center"
                            android:textFontWeight="400"
                            android:layout_gravity="center"
                            android:textColor="#868C98"
                            android:paddingLeft="4dp"
                            android:paddingRight="2dp"
                            />

                        <EditText
                            android:id="@+id/edTxtPrice"
                            android:layout_width="match_parent"
                            android:layout_height="40dp"
                            android:layout_margin="3dp"
                            android:layout_gravity="center"
                            android:inputType="number"
                            android:digits="1234567890"
                            android:textSize="14sp"
                            android:fontFamily="@font/inter"
                            android:textFontWeight="400"
                            android:maxLines="1"
                            android:background="@drawable/rounded_edittext2"
                            />
                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/percentageLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="5dp"
                    android:visibility="gone"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:text="Percentage"
                        android:textSize="14sp"
                        android:fontFamily="@font/inter"
                        android:textFontWeight="500"
                        android:textColor="#0A0D14"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/headerInclude" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="40dp"
                        android:background="@drawable/rounded_edittext"
                        android:layout_marginTop="2dp"
                        android:padding="6dp"
                        android:orientation="horizontal"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/linearDefaultHourlyRate"
                        >

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="%"
                            android:textSize="14sp"
                            android:fontFamily="@font/inter"
                            android:gravity="center"
                            android:textFontWeight="400"
                            android:layout_gravity="center"
                            android:textColor="#868C98"
                            android:paddingLeft="4dp"
                            android:paddingRight="2dp"
                            />

                        <EditText
                            android:id="@+id/edTxtPercentage"
                            android:layout_width="match_parent"
                            android:layout_height="40dp"
                            android:layout_margin="3dp"
                            android:layout_gravity="center"
                            android:inputType="number"
                            android:digits="1234567890."
                            android:textSize="14sp"
                            android:fontFamily="@font/inter"
                            android:textFontWeight="400"
                            android:maxLines="1"
                            android:background="@drawable/rounded_edittext2"
                            />
                    </LinearLayout>
                </LinearLayout>



            </LinearLayout>

        </LinearLayout>
    </LinearLayout>
    </ScrollView>
</LinearLayout>