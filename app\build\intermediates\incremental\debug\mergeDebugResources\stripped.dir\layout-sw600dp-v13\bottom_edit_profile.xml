<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_round_corners"
    android:orientation="vertical"
    android:paddingVertical="10dp"
    android:fitsSystemWindows="true"
    android:layout_marginBottom="0dp"
    app:layout_behavior="@string/bottom_sheet_behavior">


    <LinearLayout
        android:id="@+id/header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingHorizontal="15dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:paddingTop="0dp">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_marginTop="5dp"
            android:orientation="horizontal">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_toLeftOf="@+id/btnSave"
                >

                <TextView
                    android:id="@+id/addHeading"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Edit Profile"
                    android:textColor="#0A0D14"
                    android:textSize="16sp"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="500"
                    android:layout_centerHorizontal="true"
                    android:layout_centerVertical="true"
                    android:gravity="center" />
            </RelativeLayout>

            <ImageView
                android:id="@+id/backButton"
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:layout_centerVertical="true"
                android:src="@drawable/cross" />





            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnSave"
                android:layout_width="wrap_content"
                android:layout_height="48sp"
                android:layout_alignParentEnd="true"
                android:paddingVertical="0dp"
                android:textColor="#375DFB"
                android:textSize="16sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="500"
                app:cornerRadius="8dp"
                app:strokeWidth="2dp"
                app:strokeColor="#375DFB"
                app:backgroundTint="@color/white"
                android:text="Save" />
        </RelativeLayout>

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="10dp"
        android:background="#D3D3D3"
        />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        android:id="@+id/mainLayout">


        <LinearLayout
            android:id="@+id/line1"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/app_bg_color"
            android:orientation="vertical"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/headerInclude">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="15dp"
                android:layout_marginTop="15dp"
                android:fontFamily="@font/inter"
                android:text="First Name"
                android:textColor="#0A0D14"
                android:textFontWeight="500"
                android:textSize="14sp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/headerInclude" />

            <EditText
                android:id="@+id/edTxtFirstName"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                app:layout_constraintEnd_toEndOf="parent"
                android:layout_marginTop="2dp"
                android:inputType="text"
                android:digits="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
                android:maxLines="1"
                android:textSize="14sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="400"
                android:padding="12dp"
                android:background="@drawable/rounded_edittext"
                android:layout_marginHorizontal="15dp"

                />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="15dp"
                android:text="Last Name"
                android:textSize="14sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="500"
                android:layout_marginHorizontal="15dp"
                android:textColor="#0A0D14"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/headerInclude" />

            <EditText
                android:id="@+id/edTxtLastName"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                app:layout_constraintEnd_toEndOf="parent"
                android:layout_marginTop="2dp"
                android:inputType="text"
                android:digits="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
                android:maxLines="1"
                android:textSize="14sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="400"
                android:padding="12dp"
                android:background="@drawable/rounded_edittext"
                android:layout_marginHorizontal="15dp"

                />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="15dp"
                android:text="Company Name"
                android:textSize="14sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="500"
                android:layout_marginHorizontal="15dp"
                android:textColor="#0A0D14"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/headerInclude" />

            <EditText
                android:id="@+id/edTxtCompanyName"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                app:layout_constraintEnd_toEndOf="parent"
                android:layout_marginTop="2dp"
                android:inputType="text"
                android:digits="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ "
                android:maxLines="1"
                android:textSize="14sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="400"
                android:padding="12dp"
                android:background="@drawable/rounded_edittext"
                android:layout_marginHorizontal="15dp"

                />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="15dp"
                android:text="Email"
                android:textSize="14sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="500"
                android:layout_marginHorizontal="15dp"
                android:textColor="#0A0D14"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/headerInclude" />

            <EditText
                android:id="@+id/edTxtEmail"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                app:layout_constraintEnd_toEndOf="parent"
                android:layout_marginTop="2dp"
                android:inputType="textEmailAddress"
                android:maxLines="1"
                android:enabled="false"
                android:textSize="14sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="400"
                android:padding="12dp"
                android:background="@drawable/rounded_edittext2_none_editable"
                android:layout_marginHorizontal="15dp"

                />
\
        </LinearLayout>
    </ScrollView>




</LinearLayout>