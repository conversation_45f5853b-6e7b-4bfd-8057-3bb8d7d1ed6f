<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_round_corners"
    android:orientation="vertical"
    android:layout_marginTop="90dp"

    android:paddingVertical="10dp"
    app:layout_behavior="@string/bottom_sheet_behavior">


    <LinearLayout
        android:id="@+id/header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingHorizontal="15dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:paddingTop="0dp">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_marginTop="5dp"
            android:orientation="horizontal">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                >

                <TextView
                    android:id="@+id/addHeading"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Select Customer"
                    android:textSize="16sp"
                    android:textColor="#0A0D14"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="500"
                    android:layout_centerHorizontal="true"
                    android:layout_centerVertical="true"
                    android:gravity="center" />
            </RelativeLayout>

            <ImageView
                android:id="@+id/backButton"
                android:layout_width="25dp"
                android:layout_centerVertical="true"
                android:layout_height="25dp"
                android:src="@drawable/cross" />



            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnSave"
                android:layout_width="wrap_content"
                android:layout_height="48sp"
                android:layout_alignParentEnd="true"
                android:paddingVertical="0dp"
                android:textColor="#375DFB"
                android:textSize="16sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="500"
                app:cornerRadius="8dp"
                app:strokeWidth="2dp"
                app:strokeColor="#375DFB"
                app:backgroundTint="@color/white"
                android:visibility="invisible"
                android:text="Save" />

        </RelativeLayout>

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="10dp"
        android:background="#D3D3D3"
        />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:layout_marginTop="16dp"
        android:layout_marginHorizontal="15dp"
        >

        <EditText
            android:id="@+id/edTxtMaterialName"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginTop="2dp"
            android:inputType="text"
            android:hint="Search Customer"
            android:digits="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ "
            android:textSize="14sp"
            android:fontFamily="@font/inter"
            android:textFontWeight="400"
            android:maxLines="1"
            android:padding="12dp"
            android:background="@drawable/rounded_edittext"


            />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/employeeRecylerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="19dp"
            android:paddingBottom="20dp"
            app:layoutManager="LinearLayoutManager" />

        <RelativeLayout
            android:id="@+id/noCustomer"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone">

            <TextView
                android:id="@+id/noMatch"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="no matches..."
                android:textSize="16sp"
                android:textColor="#525866"
                android:fontFamily="@font/inter"
                android:textFontWeight="400"
                android:layout_marginTop="15dp"
                android:gravity="center" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/createCustomer"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:textColor="#375DFB"
                android:paddingVertical="0dp"
                android:textSize="16sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="500"
                app:cornerRadius="8dp"
                app:strokeWidth="2dp"
                app:strokeColor="#375DFB"
                app:backgroundTint="@color/white"
                android:layout_marginTop="15dp"
                android:layout_below="@+id/noMatch"
                android:text="Create New Customer" />

        </RelativeLayout>



    </LinearLayout>


</LinearLayout>