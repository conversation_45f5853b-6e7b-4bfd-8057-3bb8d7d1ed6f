<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="600dp"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/bg_round_corners"
    android:padding="24dp"
    android:layout_gravity="center">

    <!-- Header with close button -->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Select Status"
            android:textSize="18sp"
            android:fontFamily="@font/inter"
            android:textStyle="bold"
            android:textColor="@color/profit_black"
            android:layout_centerInParent="true" />

        <ImageView
            android:id="@+id/closeButton"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:src="@drawable/cross"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:padding="4dp" />

    </RelativeLayout>

    <!-- Divider -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#E0E0E0"
        android:layout_marginBottom="16dp" />

    <!-- Content with constrained width -->
    <LinearLayout
        android:layout_width="500dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:orientation="vertical">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/statusRecyclerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:nestedScrollingEnabled="false"
            android:maxHeight="400dp" />

    </LinearLayout>

</LinearLayout>
