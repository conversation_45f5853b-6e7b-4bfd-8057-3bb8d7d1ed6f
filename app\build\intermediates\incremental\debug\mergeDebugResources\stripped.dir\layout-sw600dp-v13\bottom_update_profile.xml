<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="600dp"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/bg_round_corners"
    android:padding="24dp"
    android:layout_gravity="center">

    <!-- Header with close button -->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Update profile picture"
            android:textSize="18sp"
            android:fontFamily="@font/inter"
            android:textStyle="bold"
            android:textColor="@color/profit_black"
            android:layout_centerInParent="true" />

        <ImageView
            android:id="@+id/closeButton"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:src="@drawable/cross"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:padding="4dp" />

    </RelativeLayout>

    <!-- Divider -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#E0E0E0"
        android:layout_marginBottom="16dp" />

    <!-- Content with constrained width -->
    <LinearLayout
        android:layout_width="500dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:orientation="vertical">

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/btnGallery"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:drawableStart="@android:drawable/ic_menu_gallery"
            android:drawablePadding="15dp"
            android:padding="16dp"
            android:text="Upload image"
            android:textSize="16sp"
            android:fontFamily="@font/inter"
            android:textColor="@color/profit_black"
            android:background="?attr/selectableItemBackground"
            android:gravity="start|center_vertical" />

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/btnCamera"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:drawableStart="@android:drawable/ic_menu_camera"
            android:drawablePadding="15dp"
            android:padding="16dp"
            android:text="Take a photo"
            android:textSize="16sp"
            android:fontFamily="@font/inter"
            android:textColor="@color/profit_black"
            android:background="?attr/selectableItemBackground"
            android:gravity="start|center_vertical" />

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/imageView6"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:drawableStart="@android:drawable/ic_menu_delete"
            android:drawablePadding="15dp"
            android:padding="16dp"
            android:text="Remove"
            android:textSize="16sp"
            android:fontFamily="@font/inter"
            android:textColor="@color/profit_black"
            android:background="?attr/selectableItemBackground"
            android:gravity="start|center_vertical" />

    </LinearLayout>

</LinearLayout>
