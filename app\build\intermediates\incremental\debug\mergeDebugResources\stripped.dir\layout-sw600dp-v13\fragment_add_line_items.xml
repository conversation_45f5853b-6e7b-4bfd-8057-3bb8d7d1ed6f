<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/coss"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/app_bg_color"
    tools:context="com.manaknight.app.ui.fragments.LoginFragment">


    <include
        android:id="@+id/headerInclude"
        layout="@layout/header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>

    <RelativeLayout
        android:id="@+id/container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintWidth_max="500dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/headerInclude"
        app:layout_constraintBottom_toBottomOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/innerConstraintLayout"
            android:layout_width="500dp"
            android:layout_height="match_parent"
            android:layout_centerInParent="true">

            <LinearLayout
                android:id="@+id/dataLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="visible"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16sp"
                    android:background="@drawable/rounded_edittext"
                    android:orientation="vertical"
                    android:paddingHorizontal="12dp"
                    android:paddingVertical="10dp"
                    app:layout_constraintStart_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/lineItem"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:fontFamily="@font/inter"
                            android:text="Line Item #1"
                            android:textColor="@color/profit_black"
                            android:textFontWeight="500"
                            android:textSize="14sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_marginTop="10dp"
                            android:fontFamily="@font/inter"
                            android:text="Description"
                            android:textColor="@color/profit_black"
                            android:textFontWeight="500"
                            android:textSize="14sp" />

                        <EditText
                            android:id="@+id/descInput"
                            android:layout_width="match_parent"
                            android:layout_height="130dp"
                            android:layout_marginTop="8dp"
                            android:background="@drawable/rounded_edittext"
                            android:fontFamily="@font/inter"

                            android:gravity="top"
                            android:hint="Enter description"
                            android:padding="10dp"
                            android:textFontWeight="500"

                            android:textSize="14sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_marginTop="10dp"
                            android:fontFamily="@font/inter"
                            android:text="Estimate by"
                            android:textColor="@color/profit_black"

                            android:textFontWeight="500"
                            android:textSize="14sp" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:orientation="horizontal">

                            <com.google.android.material.button.MaterialButton
                                android:id="@+id/btnMaterial"
                                android:layout_width="0dp"
                                android:layout_height="44dp"
                                android:paddingVertical="0dp"
                                android:layout_marginEnd="10dp"
                                android:layout_weight="1"
                                android:fontFamily="@font/inter"
                                android:text="Material"
                                android:textColor="@color/black"
                                android:textFontWeight="400"
                                android:textSize="13sp"
                                app:backgroundTint="@color/white"
                                app:cornerRadius="8dp"
                                app:strokeColor="@color/black"
                                app:strokeWidth="1dp" />

                            <com.google.android.material.button.MaterialButton
                                android:id="@+id/btnLinearFt"
                                android:layout_width="0dp"
                                android:layout_height="44dp"
                                android:layout_marginEnd="10dp"
                                android:paddingVertical="0dp"
                                android:layout_weight="1"
                                android:fontFamily="@font/inter"
                                android:text="Linear ft"
                                android:textColor="@color/black"
                                android:textFontWeight="400"
                                android:textSize="13sp"
                                app:backgroundTint="@color/white"
                                app:cornerRadius="8dp"
                                app:strokeColor="@color/black"
                                app:strokeWidth="1dp" />

                            <com.google.android.material.button.MaterialButton
                                android:id="@+id/btnSquareFt"
                                android:layout_width="0dp"
                                android:layout_height="44dp"
                                android:layout_weight="1"
                                android:paddingVertical="0dp"
                                android:fontFamily="@font/inter"
                                android:text="Square ft"
                                android:textColor="@color/black"
                                android:textFontWeight="400"
                                android:textSize="13sp"
                                app:backgroundTint="@color/white"
                                app:cornerRadius="8dp"
                                app:strokeColor="@color/black"
                                app:strokeWidth="1dp" />

                        </LinearLayout>


                    </LinearLayout>


                </LinearLayout>


            </LinearLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </RelativeLayout>




</androidx.constraintlayout.widget.ConstraintLayout>
