<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/app_bg_color">


    <include
        android:id="@+id/headerInclude"
        layout="@layout/header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>

    <RelativeLayout
        android:id="@+id/container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintWidth_max="500dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/headerInclude"
        app:layout_constraintBottom_toBottomOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/innerConstraintLayout"
            android:layout_width="500dp"
            android:layout_height="match_parent"
            android:layout_centerInParent="true">
            <ScrollView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_marginTop="30dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                >

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">



                    <LinearLayout
                        android:id="@+id/linearDefaultHourlyRate3"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@drawable/rounded_edittext"
                        android:padding="15dp"
                        android:layout_marginTop="15dp"
                        android:orientation="vertical"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/linearDefaultHourlyRate2"
                        >

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">

                            <ImageView
                                android:layout_width="wrap_content"
                                android:layout_height="24dp"
                                android:layout_gravity="center"
                                android:src="@drawable/green_check" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                android:layout_marginLeft="10dp"
                                android:text="You’re all set!"
                                android:textColor="#000000"
                                android:textSize="14sp"
                                android:fontFamily="@font/inter"
                                android:textFontWeight="500"
                                />
                        </LinearLayout>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:textSize="14sp"
                            android:fontFamily="@font/inter"
                            android:textFontWeight="400"
                            android:text="Account has been successfully set up."
                            android:textColor="#525866"
                            />


                    </LinearLayout>


                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnContinue"
                        android:layout_width="match_parent"
                        android:layout_height="54dp"
                        android:layout_marginTop="10dp"
                        android:paddingHorizontal="48dp"
                        android:paddingVertical="0dp"
                        android:textColor="@color/black"
                        android:textSize="16sp"
                        android:fontFamily="@font/inter"
                        android:textFontWeight="600"
                        app:cornerRadius="8dp"
                        app:backgroundTint="@color/brand_green"
                        android:text="Finish"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/linearDefaultProfitOverhead3" />
                </LinearLayout>

            </ScrollView>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </RelativeLayout>









</androidx.constraintlayout.widget.ConstraintLayout>
