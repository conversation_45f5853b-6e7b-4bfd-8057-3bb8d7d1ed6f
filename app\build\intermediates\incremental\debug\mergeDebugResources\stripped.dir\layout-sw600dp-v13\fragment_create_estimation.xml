<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/constraint"
    android:background="@color/app_bg_color">


    <include
        android:id="@+id/headerInclude"
        layout="@layout/header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>

    <RelativeLayout
        android:id="@+id/container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintWidth_max="500dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/headerInclude"
        app:layout_constraintBottom_toBottomOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/innerConstraintLayout"
            android:layout_width="500dp"
            android:layout_height="match_parent"
            android:layout_centerInParent="true">
            <ScrollView
                android:id="@+id/scrollable"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:focusableInTouchMode="true"
                android:layout_marginTop="30dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                >

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:id="@+id/line1"
                    android:orientation="vertical">


                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Customer"
                        android:textSize="14sp"
                        android:fontFamily="@font/inter"
                        android:textFontWeight="400"
                        android:layout_centerVertical="true"
                        android:textColor="@color/profit_black"/>

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnSearch"
                        android:layout_width="match_parent"
                        android:layout_height="54dp"
                        android:paddingVertical="0dp"
                        android:layout_marginTop="2dp"
                        android:textColor="#868C98"
                        app:cornerRadius="8dp"
                        app:strokeColor="#E2E4E9"
                        app:strokeWidth="1dp"
                        app:backgroundTint="@color/white"
                        android:textSize="16sp"
                        android:fontFamily="@font/inter"
                        android:textFontWeight="500"
                        android:text="- Select -"
                        android:textAlignment="textStart"
                        app:icon="@drawable/chevron_bottom"
                        app:iconGravity="end"
                        app:iconTint="#525866"/>

                    <LinearLayout
                        android:id="@+id/dataLayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:visibility="gone"
                        >

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"

                            android:paddingHorizontal="12dp"
                            android:paddingVertical="10dp"
                            android:layout_marginTop="16sp"
                            android:background="@drawable/rounded_edittext"
                            app:layout_constraintStart_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical"
                                android:layout_marginTop="10dp">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Address"
                                    android:textSize="16sp"
                                    android:fontFamily="@font/inter"
                                    android:textFontWeight="400"
                                    android:layout_centerVertical="true"
                                    android:textColor="#525866"/>

                                <TextView
                                    android:id="@+id/txtAddress"
                                    android:textAlignment="textEnd"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Kapil Kumar"
                                    android:textSize="16sp"
                                    android:fontFamily="@font/inter"
                                    android:textFontWeight="500"
                                    android:layout_alignParentEnd="true"
                                    android:layout_centerVertical="true"
                                    android:textColor="#000000"/>

                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical"
                                android:layout_marginTop="10dp">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Email"
                                    android:textSize="16sp"
                                    android:fontFamily="@font/inter"
                                    android:textFontWeight="400"
                                    android:layout_centerVertical="true"
                                    android:textColor="#525866"/>

                                <TextView
                                    android:id="@+id/txtEmail"
                                    android:textAlignment="textEnd"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="{Email}"
                                    android:textSize="16sp"
                                    android:fontFamily="@font/inter"
                                    android:textFontWeight="500"
                                    android:layout_alignParentEnd="true"
                                    android:layout_centerVertical="true"
                                    android:textColor="#000000"/>

                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical"
                                android:layout_marginTop="10dp">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Phone Number"
                                    android:textSize="16sp"
                                    android:fontFamily="@font/inter"
                                    android:textFontWeight="400"
                                    android:layout_centerVertical="true"
                                    android:textColor="#525866"/>

                                <TextView
                                    android:id="@+id/txtPhoneNumber"
                                    android:textAlignment="textEnd"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="{Phone Number}"
                                    android:textSize="16sp"
                                    android:fontFamily="@font/inter"
                                    android:textFontWeight="500"
                                    android:layout_alignParentEnd="true"
                                    android:layout_centerVertical="true"
                                    android:textColor="#000000"/>

                            </LinearLayout>


                        </LinearLayout>


                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btnEditCustomer"
                            android:layout_width="match_parent"
                            android:layout_height="54dp"
                            android:layout_marginTop="20dp"
                            android:paddingVertical="0dp"
                            android:paddingHorizontal="48dp"
                            android:textColor="@color/black"
                            app:cornerRadius="8dp"
                            app:strokeColor="@color/black"
                            app:strokeWidth="2dp"
                            app:backgroundTint="@color/white"
                            android:textSize="16sp"
                            android:fontFamily="@font/inter"
                            android:textFontWeight="600"
                            android:text="Edit Customer"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/linearDefaultProfitOverhead3" />

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btnContinue"
                            android:layout_width="match_parent"
                            android:layout_height="54dp"
                            android:paddingVertical="0dp"
                            android:layout_marginTop="10dp"
                            android:paddingHorizontal="48dp"
                            android:textColor="@color/white"
                            app:cornerRadius="8dp"
                            app:backgroundTint="@color/black"
                            android:textSize="16sp"
                            android:fontFamily="@font/inter"
                            android:textFontWeight="600"
                            android:text="Continue"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/linearDefaultProfitOverhead3" />
                    </LinearLayout>


                </LinearLayout>

            </ScrollView>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </RelativeLayout>










</androidx.constraintlayout.widget.ConstraintLayout>
