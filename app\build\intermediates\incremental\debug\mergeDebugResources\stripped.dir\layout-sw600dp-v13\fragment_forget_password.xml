<?xml version="1.0" encoding="utf-8"?>

<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/relativeLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/app_bg_color"
    android:orientation="vertical">


        <include
            android:id="@+id/headerInclude"
            layout="@layout/header"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>

        <RelativeLayout
            android:id="@+id/container"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintWidth_max="500dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/headerInclude"
            app:layout_constraintBottom_toBottomOf="parent">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/innerConstraintLayout"
                    android:layout_width="500dp"
                    android:layout_height="match_parent"
                    android:layout_centerInParent="true">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="30dp"
                            android:orientation="vertical"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            >

                                <TextView
                                    android:id="@+id/textView2"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:fontFamily="@font/inter"
                                    android:text="We will email you a link to reset your password."
                                    android:textColor="#525866"
                                    android:textFontWeight="400"
                                    android:textSize="14sp" />


                                <TextView
                                    android:id="@+id/tvEmail"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="15dp"
                                    android:fontFamily="@font/inter"
                                    android:text="Email"
                                    android:textColor="#0A0D14"
                                    android:textFontWeight="500"
                                    android:textSize="14sp"
                                    app:layout_constraintStart_toStartOf="parent"
                                    app:layout_constraintTop_toBottomOf="@+id/headerInclude" />

                                <EditText
                                    android:id="@+id/edTxtForgetPassword"
                                    android:layout_width="match_parent"
                                    android:layout_height="40dp"
                                    android:layout_marginTop="2dp"
                                    android:background="@drawable/rounded_edittext"
                                    android:fontFamily="@font/inter"
                                    android:inputType="textEmailAddress"
                                    android:maxLines="1"
                                    android:paddingHorizontal="12dp"
                                    android:textFontWeight="400"
                                    android:textSize="14sp"
                                    app:layout_constraintEnd_toEndOf="parent"
                                    app:layout_constraintStart_toStartOf="parent"
                                    app:layout_constraintTop_toBottomOf="@+id/tvEmail" />


                                <com.google.android.material.button.MaterialButton
                                    android:id="@+id/continueButton"
                                    android:layout_width="match_parent"
                                    android:layout_height="54dp"
                                    android:paddingVertical="0dp"
                                    android:layout_marginTop="15dp"
                                    android:fontFamily="@font/inter"
                                    android:paddingHorizontal="48dp"
                                    android:text="Request Link"
                                    android:textColor="@color/white"
                                    android:textFontWeight="600"
                                    android:textSize="16sp"
                                    app:backgroundTint="@color/black"
                                    app:cornerRadius="8dp" />
                        </LinearLayout>

                </androidx.constraintlayout.widget.ConstraintLayout>
        </RelativeLayout>



</androidx.constraintlayout.widget.ConstraintLayout>

    