<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/coss"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/app_bg_color"
    tools:context="com.manaknight.app.ui.fragments.LoginFragment">


    <include
        android:id="@+id/headerInclude"
        layout="@layout/header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>

    <RelativeLayout
        android:id="@+id/container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintWidth_max="500dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/headerInclude"
        app:layout_constraintBottom_toBottomOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/innerConstraintLayout"
            android:layout_width="500dp"
            android:layout_height="match_parent"
            android:layout_centerInParent="true">

            <LinearLayout
                android:id="@+id/topLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="1dp"
                android:orientation="vertical"
                android:paddingTop="0dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_marginTop="10dp"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_weight="1"
                        android:fontFamily="@font/inter"
                        android:gravity="center"
                        android:text="Line Items"
                        android:textColor="@color/profit_black"
                        android:textFontWeight="500"
                        android:textSize="16sp" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnAddLineItem"
                        android:layout_width="wrap_content"
                        android:layout_height="48sp"
                        android:paddingVertical="0dp"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:fontFamily="@font/inter"
                        android:text="Add Line Item"
                        android:textColor="#375DFB"
                        android:textFontWeight="500"
                        android:textSize="16sp"
                        app:backgroundTint="@color/white"
                        app:cornerRadius="8dp"
                        app:strokeColor="#375DFB"
                        app:strokeWidth="2dp" />

                </RelativeLayout>
            </LinearLayout>

            <ScrollView
                android:id="@+id/scrollableContent"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_marginTop="1dp"
                android:layout_marginBottom="5dp"
                android:descendantFocusability="beforeDescendants"
                android:fillViewport="true"
                android:focusableInTouchMode="true"
                android:visibility="gone"
                app:layout_constraintBottom_toTopOf="@id/totalLineItem"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/topLayout">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:id="@+id/dynamicLineItemsContainer"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <!-- Line items will be added here dynamically -->

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/line1"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <include
                            android:id="@+id/materialLineItem"
                            layout="@layout/item_line"

                            />

                        <include
                            android:id="@+id/linearLineItem"
                            layout="@layout/item_line"

                            />

                        <include
                            android:id="@+id/squareLineItem"
                            layout="@layout/item_line"

                            />
                    </LinearLayout>

                </LinearLayout>

            </ScrollView>

            <include
                android:id="@+id/totalLineItem"
                layout="@layout/item_line_total"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"

                />

            <RelativeLayout
                android:id="@+id/noCustomer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="visible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"

                app:layout_constraintTop_toBottomOf="@id/topLayout">

                <TextView
                    android:id="@+id/noMatch"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerHorizontal="true"
                    android:layout_centerVertical="true"
                    android:layout_marginTop="15dp"
                    android:layout_weight="1"
                    android:fontFamily="@font/inter"
                    android:gravity="center"
                    android:text="You haven't added any line items. Please add a line item to proceed."
                    android:textColor="#525866"
                    android:textFontWeight="400"
                    android:textSize="14sp" />


            </RelativeLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </RelativeLayout>




</androidx.constraintlayout.widget.ConstraintLayout>
