<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/coss"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/app_bg_color"
    tools:context="com.manaknight.app.ui.fragments.LoginFragment">


    <include
        android:id="@+id/headerInclude"
        layout="@layout/header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>

    <RelativeLayout
        android:id="@+id/container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintWidth_max="500dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/headerInclude"
        app:layout_constraintBottom_toBottomOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/innerConstraintLayout"
            android:layout_width="500dp"
            android:layout_height="match_parent"
            android:layout_centerInParent="true">

            <LinearLayout
                android:id="@+id/topLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="1dp"
                android:orientation="vertical"
                android:paddingTop="0dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_marginTop="10dp"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_weight="1"
                        android:fontFamily="@font/inter"
                        android:gravity="center"
                        android:text="Linear Foot Costs"
                        android:textColor="#525866"
                        android:textFontWeight="500"
                        android:textSize="15sp" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnAddLineItem"
                        android:layout_width="wrap_content"
                        android:layout_height="48sp"
                        android:paddingVertical="0dp"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:fontFamily="@font/inter"
                        android:text="Add New Cost"
                        android:textColor="#375DFB"
                        android:textFontWeight="500"
                        android:textSize="16sp"
                        app:backgroundTint="@color/white"
                        app:cornerRadius="8dp"
                        app:strokeColor="#375DFB"
                        app:strokeWidth="2dp" />

                </RelativeLayout>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/line1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"

                android:layout_marginTop="1dp"
                android:layout_marginBottom="20dp"
                android:orientation="vertical"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/topLayout">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/linealRecylerView"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="10dp"
                    android:paddingBottom="20dp"

                    app:layoutManager="LinearLayoutManager" />
            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>
    </RelativeLayout>





</androidx.constraintlayout.widget.ConstraintLayout>
