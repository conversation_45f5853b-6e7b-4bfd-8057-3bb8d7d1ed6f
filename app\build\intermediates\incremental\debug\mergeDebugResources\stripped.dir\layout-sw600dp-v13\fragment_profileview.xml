<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/constraint"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/app_bg_color">


    <include
        android:id="@+id/headerInclude"
        layout="@layout/header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>

    <RelativeLayout
        android:id="@+id/container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintWidth_max="500dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/headerInclude"
        app:layout_constraintBottom_toBottomOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/innerConstraintLayout"
            android:layout_width="500dp"
            android:layout_height="match_parent"
            android:layout_centerInParent="true">

            <ScrollView
                android:id="@+id/scrollable"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="30dp"
                android:focusableInTouchMode="true"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <LinearLayout
                    android:id="@+id/line1"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">


                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@drawable/rounded_edittext"
                        android:orientation="vertical"
                        android:paddingHorizontal="12dp"
                        android:paddingVertical="10dp"
                        app:layout_constraintStart_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent">


                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_centerVertical="true"
                                android:fontFamily="@font/inter"
                                android:text="Profile Details"
                                android:textColor="#000000"
                                android:textFontWeight="500"
                                android:textSize="14sp" />

                            <ImageView
                                android:id="@+id/btnEdit"
                                android:layout_width="32dp"
                                android:layout_height="32dp"
                                android:layout_alignParentEnd="true"
                                android:layout_centerVertical="true"
                                android:layout_marginRight="5dp"
                                android:src="@drawable/edit" />


                        </RelativeLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_centerVertical="true"
                                android:fontFamily="@font/inter"
                                android:text="First Name"
                                android:textColor="#525866"
                                android:textFontWeight="400"
                                android:textSize="12sp" />

                            <TextView
                                android:id="@+id/txtFName"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_alignParentEnd="true"
                                android:layout_centerVertical="true"
                                android:fontFamily="@font/inter"
                                android:text="Kapil Kumar"
                                android:textAlignment="textEnd"
                                android:textColor="#000000"
                                android:textFontWeight="500"
                                android:textSize="14sp" />

                        </LinearLayout>

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:layout_marginTop="10dp"
                            android:layout_marginBottom="10dp"
                            android:background="#E2E4E9" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_centerVertical="true"
                                android:fontFamily="@font/inter"
                                android:text="Last Name"
                                android:textColor="#525866"
                                android:textFontWeight="400"
                                android:textSize="12sp" />

                            <TextView
                                android:id="@+id/txtLName"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_alignParentEnd="true"
                                android:layout_centerVertical="true"
                                android:fontFamily="@font/inter"
                                android:text="Kapil Kumar"
                                android:textAlignment="textEnd"
                                android:textColor="#000000"
                                android:textFontWeight="500"
                                android:textSize="14sp" />

                        </LinearLayout>

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:layout_marginTop="10dp"
                            android:layout_marginBottom="10dp"
                            android:background="#E2E4E9" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_centerVertical="true"
                                android:fontFamily="@font/inter"
                                android:text="Company Name"
                                android:textColor="#525866"
                                android:textFontWeight="400"
                                android:textSize="12sp" />

                            <TextView
                                android:id="@+id/txtCompanyName"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_alignParentEnd="true"
                                android:layout_centerVertical="true"
                                android:fontFamily="@font/inter"
                                android:text="Kapil Kumar"
                                android:textAlignment="textEnd"
                                android:textColor="#000000"
                                android:textFontWeight="500"
                                android:textSize="14sp" />

                        </LinearLayout>

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:layout_marginTop="10dp"
                            android:layout_marginBottom="10dp"
                            android:background="#E2E4E9" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_centerVertical="true"
                                android:fontFamily="@font/inter"
                                android:text="Email"
                                android:textColor="#525866"
                                android:textFontWeight="400"
                                android:textSize="12sp" />

                            <TextView
                                android:id="@+id/txtEmail"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_alignParentEnd="true"
                                android:layout_centerVertical="true"
                                android:fontFamily="@font/inter"
                                android:text="Kapil Kumar"
                                android:textAlignment="textEnd"
                                android:textColor="#000000"
                                android:textFontWeight="500"
                                android:textSize="14sp" />

                        </LinearLayout>


                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:background="@drawable/rounded_edittext"
                        android:orientation="vertical"
                        android:paddingHorizontal="12dp"
                        android:paddingVertical="10dp"
                        app:layout_constraintStart_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent">


                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_centerVertical="true"
                                android:fontFamily="@font/inter"
                                android:text="Security"
                                android:textColor="#000000"
                                android:textFontWeight="500"
                                android:textSize="14sp" />


                        </RelativeLayout>


                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btnContinue"
                            android:layout_width="match_parent"
                            android:layout_height="54dp"
                            android:paddingVertical="0dp"
                            android:layout_marginTop="10dp"
                            android:fontFamily="@font/inter"
                            android:paddingHorizontal="48dp"
                            android:text="Update Password"
                            android:textColor="@color/white"
                            android:textFontWeight="600"
                            android:textSize="16sp"
                            app:backgroundTint="@color/black"
                            app:cornerRadius="8dp"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/linearDefaultProfitOverhead3" />


                    </LinearLayout>


                </LinearLayout>

            </ScrollView>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </RelativeLayout>





</androidx.constraintlayout.widget.ConstraintLayout>
