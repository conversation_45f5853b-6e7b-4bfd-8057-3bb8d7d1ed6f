<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/constraint"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/app_bg_color"
    tools:context="com.manaknight.app.ui.fragments.LoginFragment">


    <include
        android:id="@+id/headerInclude"
        layout="@layout/header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>

    <RelativeLayout
        android:id="@+id/container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintWidth_max="500dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/headerInclude"
        app:layout_constraintBottom_toBottomOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/innerConstraintLayout"
            android:layout_width="500dp"
            android:layout_height="match_parent"
            android:layout_centerInParent="true">


        <TextView
            android:id="@+id/tvCode"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="30dp"
            android:fontFamily="@font/inter"
            android:text="Code"
            android:textColor="#0A0D14"
            android:textFontWeight="500"
            android:textSize="14sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <EditText
            android:id="@+id/edTxtCode"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_marginTop="2dp"
            android:background="@drawable/rounded_edittext"
            android:fontFamily="@font/inter"
            android:inputType="text"
            android:maxLines="1"
            android:paddingHorizontal="12dp"
            android:textFontWeight="400"
            android:textSize="14sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvCode" />

        <TextView
            android:id="@+id/tvPassword"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp"
            android:fontFamily="@font/inter"
            android:text="Set New Password"
            android:textColor="#0A0D14"
            android:textFontWeight="500"
            android:textSize="14sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/edTxtCode" />

        <EditText
            android:id="@+id/edTxtPassword"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_marginTop="2dp"
            android:background="@drawable/rounded_edittext"
            android:fontFamily="@font/inter"
            android:inputType="textPassword"
            android:maxLines="1"
            android:paddingHorizontal="12dp"
            android:textFontWeight="400"
            android:textSize="14sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvPassword" />


        <TextView
            android:id="@+id/tvPasswordConfirm"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp"
            android:fontFamily="@font/inter"
            android:text="Confirm New Password"
            android:textColor="#0A0D14"
            android:textFontWeight="500"
            android:textSize="14sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/edTxtPassword" />

        <EditText
            android:id="@+id/edTxtPasswordConfirm"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_marginTop="2dp"
            android:background="@drawable/rounded_edittext"
            android:fontFamily="@font/inter"
            android:inputType="textPassword"
            android:maxLines="1"
            android:paddingHorizontal="12dp"
            android:textFontWeight="400"
            android:textSize="14sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvPasswordConfirm" />


        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnResetPassword"
            android:layout_width="match_parent"
            android:layout_height="54dp"
            android:paddingVertical="0dp"
            android:layout_marginTop="15dp"
            android:fontFamily="@font/inter"
            android:paddingHorizontal="48dp"
            android:text="Set New Password"
            android:textColor="@color/white"
            android:textFontWeight="600"
            android:textSize="16sp"
            app:backgroundTint="@color/black"
            app:cornerRadius="8dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/edTxtPasswordConfirm" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </RelativeLayout>



</androidx.constraintlayout.widget.ConstraintLayout>
    