<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/coss"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/app_bg_color"
    tools:context="com.manaknight.app.ui.fragments.LoginFragment">


    <include
        android:id="@+id/headerInclude"
        layout="@layout/header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>

    <RelativeLayout
        android:id="@+id/container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintWidth_max="500dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/headerInclude"
        app:layout_constraintBottom_toBottomOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/innerConstraintLayout"
            android:layout_width="500dp"
            android:layout_height="match_parent"
            android:layout_centerInParent="true">

            <ScrollView
                android:id="@+id/scrollableContent"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_marginTop="20dp"
                android:layout_marginBottom="20dp"
                android:descendantFocusability="beforeDescendants"
                android:fillViewport="true"
                android:focusableInTouchMode="true"
                app:layout_constraintBottom_toTopOf="@+id/priv"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <LinearLayout
                    android:id="@+id/line1"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvFirstName"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/inter"
                            android:text="First Name"
                            android:textColor="#0A0D14"
                            android:textFontWeight="500"
                            android:textSize="14sp"
                            tools:ignore="MissingConstraints" />

                        <EditText
                            android:id="@+id/edTxtFirstName"
                            android:layout_width="match_parent"
                            android:layout_height="40dp"
                            android:layout_marginTop="2dp"
                            android:background="@drawable/rounded_edittext"
                            android:digits="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ "
                            android:fontFamily="@font/inter"
                            android:inputType="text"
                            android:maxLines="1"
                            android:paddingHorizontal="12dp"
                            android:textFontWeight="400"
                            android:textSize="14sp" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="15dp"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvLastName"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/inter"
                            android:text="Last Name"
                            android:textColor="#0A0D14"
                            android:textFontWeight="500"
                            android:textSize="14sp" />

                        <EditText
                            android:id="@+id/edTxtLastName"
                            android:layout_width="match_parent"
                            android:layout_height="40dp"
                            android:layout_marginTop="2dp"
                            android:background="@drawable/rounded_edittext"
                            android:digits="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ "
                            android:fontFamily="@font/inter"
                            android:inputType="text"
                            android:maxLines="1"
                            android:paddingHorizontal="12dp"
                            android:textFontWeight="400"
                            android:textSize="14sp"
                            app:layout_constraintEnd_toEndOf="parent" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="15dp"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvCompanyName"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/inter"
                            android:text="Company Name"
                            android:textColor="#0A0D14"
                            android:textFontWeight="500"
                            android:textSize="14sp" />

                        <EditText
                            android:id="@+id/edTxtCompanyName"
                            android:layout_width="match_parent"
                            android:layout_height="40dp"
                            android:layout_marginTop="2dp"
                            android:background="@drawable/rounded_edittext"
                            android:digits="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ "
                            android:fontFamily="@font/inter"
                            android:inputType="text"
                            android:maxLines="1"
                            android:paddingHorizontal="12dp"
                            android:textFontWeight="400"
                            android:textSize="14sp" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="15dp"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvEmail"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/inter"
                            android:text="Email"
                            android:textColor="#0A0D14"
                            android:textFontWeight="500"
                            android:textSize="14sp" />

                        <EditText
                            android:id="@+id/edTxtUserName"
                            android:layout_width="match_parent"
                            android:layout_height="40dp"
                            android:layout_marginTop="2dp"
                            android:background="@drawable/rounded_edittext"
                            android:fontFamily="@font/inter"
                            android:inputType="textEmailAddress"
                            android:maxLines="1"
                            android:paddingHorizontal="12dp"
                            android:textFontWeight="400"
                            android:textSize="14sp" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="15dp"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvPassword"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/inter"
                            android:text="Password"
                            android:textColor="#0A0D14"
                            android:textFontWeight="500"
                            android:textSize="14sp" />

                        <EditText
                            android:id="@+id/edTxtPassword"
                            android:layout_width="match_parent"
                            android:layout_height="40dp"
                            android:layout_marginTop="2dp"
                            android:background="@drawable/rounded_edittext"
                            android:fontFamily="@font/inter"
                            android:inputType="textPassword"
                            android:maxLines="1"
                            android:paddingHorizontal="12dp"
                            android:textFontWeight="400"
                            android:textSize="14sp"

                            />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:orientation="vertical">

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btnSignUp"
                            android:layout_width="match_parent"
                            android:layout_height="54dp"
                            android:paddingVertical="0dp"
                            android:fontFamily="@font/inter"
                            android:paddingHorizontal="48dp"
                            android:text="Continue"
                            android:textColor="@color/white"
                            android:textFontWeight="600"
                            android:textSize="16sp"
                            app:backgroundTint="@color/black"
                            app:cornerRadius="8dp" />


                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginTop="10dp"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/btnSignin"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/inter"
                            android:text="@string/already_have_account"
                            android:textColor="#525866"
                            android:textFontWeight="400"
                            android:textSize="14sp"
                            android:visibility="visible" />


                    </LinearLayout>

                </LinearLayout>


            </ScrollView>

            <LinearLayout
                android:id="@+id/priv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="30dp"
                android:orientation="horizontal"
                android:padding="20sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent">

                <TextView
                    android:id="@+id/btnTerms"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/inter"
                    android:text="Terms and Conditions"
                    android:textColor="#868C98"
                    android:textFontWeight="500"
                    android:textSize="12sp" />

                <TextView
                    android:id="@+id/btnPolicy"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="20sp"
                    android:fontFamily="@font/inter"
                    android:text="Privacy Policy"
                    android:textColor="#868C98"
                    android:textFontWeight="500"
                    android:textSize="12sp" />

            </LinearLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </RelativeLayout>




</androidx.constraintlayout.widget.ConstraintLayout>
