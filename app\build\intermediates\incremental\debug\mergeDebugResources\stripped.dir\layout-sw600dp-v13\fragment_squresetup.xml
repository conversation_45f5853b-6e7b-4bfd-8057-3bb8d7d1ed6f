<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/app_bg_color">


    <include
        android:id="@+id/headerInclude"
        layout="@layout/header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>

    <RelativeLayout
        android:id="@+id/container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintWidth_max="500dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/headerInclude"
        app:layout_constraintBottom_toBottomOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/innerConstraintLayout"
            android:layout_width="500dp"
            android:layout_height="match_parent"
            android:layout_centerInParent="true">

            <ScrollView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="30dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">


                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/squareRecylerView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:paddingBottom="20dp"
                        app:layoutManager="LinearLayoutManager" />


                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnAddSquare"
                        android:layout_width="match_parent"
                        android:layout_height="54dp"
                        android:paddingVertical="0dp"
                        android:layout_marginTop="15dp"
                        android:fontFamily="@font/inter"
                        android:paddingHorizontal="2dp"
                        android:text="+ Add Square Foot Cost"
                        android:textColor="#375DFB"
                        android:textFontWeight="600"
                        android:textSize="16sp"

                        app:backgroundTint="@color/white"
                        app:cornerRadius="8dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/linearDefaultProfitOverhead3"
                        app:strokeColor="#375DFB"
                        app:strokeWidth="2dp" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnContinue"
                        android:layout_width="match_parent"
                        android:layout_height="54dp"
                        android:paddingVertical="0dp"
                        android:layout_marginTop="10dp"
                        android:fontFamily="@font/inter"
                        android:paddingHorizontal="48dp"
                        android:text="Continue"
                        android:textColor="@color/white"
                        android:textFontWeight="600"
                        android:textSize="16sp"

                        app:backgroundTint="@color/black"
                        app:cornerRadius="8dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/linearDefaultProfitOverhead3" />
                </LinearLayout>

            </ScrollView>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </RelativeLayout>




</androidx.constraintlayout.widget.ConstraintLayout>
