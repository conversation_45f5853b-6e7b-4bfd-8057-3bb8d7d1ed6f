<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/app_bg_color"
    tools:context="com.manaknight.app.ui.fragments.LoginFragment">


    <include
        android:id="@+id/headerInclude"
        layout="@layout/header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>

    <RelativeLayout
        android:id="@+id/container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintWidth_max="500dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/headerInclude"
        app:layout_constraintBottom_toBottomOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/innerConstraintLayout"
            android:layout_width="500dp"
            android:layout_height="match_parent"
            android:layout_centerInParent="true">

            <androidx.cardview.widget.CardView
                android:id="@+id/dialogCardView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginVertical="20dp"
                app:cardBackgroundColor="@color/white"
                app:cardCornerRadius="16dp"
                app:cardElevation="1dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"

                    android:paddingBottom="10dp">


                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="20dp"
                        android:layout_marginHorizontal="15dp"
                        android:layout_marginTop="15dp"
                        android:background="@drawable/rounded_textview_bg"
                        android:fontFamily="@font/inter"
                        android:gravity="center"
                        android:paddingHorizontal="10dp"
                        android:paddingVertical="1dp"
                        android:text="FREE TRIAL"
                        android:textColor="@color/black"
                        android:textFontWeight="500"
                        android:textSize="12sp" />


                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:paddingHorizontal="15dp">

                        <TextView
                            android:id="@+id/tvPrice"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:fontFamily="@font/inter"
                            android:text="$45"
                            android:textColor="#0A0D14"
                            android:textFontWeight="700"
                            android:textSize="20sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_toRightOf="@+id/tvPrice"
                            android:fontFamily="@font/inter"
                            android:text="/month"
                            android:textColor="#0A0D14"
                            android:textFontWeight="400"
                            android:textSize="18sp" />


                    </RelativeLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginHorizontal="15dp"
                        android:layout_marginTop="10dp"

                        android:layout_marginBottom="10dp"
                        android:background="#E2E4E9" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:fontFamily="@font/inter"
                        android:paddingHorizontal="15dp"
                        android:text="Start a 14-day free trial for full access to Profit Pro! Manage your business, Know your numbers without the hastle of spreadsheets or miscalculations."
                        android:textColor="#525866"
                        android:textFontWeight="400"
                        android:textSize="14sp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:orientation="horizontal"
                        android:paddingHorizontal="15dp">


                        <ImageView
                            android:layout_width="15dp"
                            android:layout_height="15dp"
                            android:layout_gravity="center"
                            android:src="@drawable/check" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginLeft="10dp"
                            android:fontFamily="@font/inter"
                            android:text="Precision estimating"
                            android:textColor="#000000"
                            android:textFontWeight="400"
                            android:textSize="14sp" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:orientation="horizontal"
                        android:paddingHorizontal="15dp">


                        <ImageView
                            android:layout_width="15dp"
                            android:layout_height="15dp"
                            android:layout_gravity="center"
                            android:src="@drawable/check" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginLeft="10dp"
                            android:fontFamily="@font/inter"
                            android:text="Job costing"
                            android:textColor="#000000"
                            android:textFontWeight="400"
                            android:textSize="14sp" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:orientation="horizontal"
                        android:paddingHorizontal="15dp">


                        <ImageView
                            android:layout_width="15dp"
                            android:layout_height="15dp"
                            android:layout_gravity="center"
                            android:src="@drawable/check" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginLeft="10dp"
                            android:fontFamily="@font/inter"
                            android:text="Payment tracking"
                            android:textColor="#000000"
                            android:textFontWeight="400"
                            android:textSize="14sp" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:orientation="horizontal"
                        android:paddingHorizontal="15dp">


                        <ImageView
                            android:layout_width="15dp"
                            android:layout_height="15dp"
                            android:layout_gravity="center"
                            android:src="@drawable/check" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginLeft="10dp"
                            android:fontFamily="@font/inter"
                            android:text="Autogenerated pdf documents"
                            android:textColor="#000000"
                            android:textFontWeight="400"
                            android:textSize="14sp" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:layout_marginBottom="10dp"
                        android:orientation="horizontal"
                        android:paddingHorizontal="15dp">


                        <ImageView
                            android:layout_width="15dp"
                            android:layout_height="15dp"
                            android:layout_gravity="center"
                            android:src="@drawable/check" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginLeft="10dp"
                            android:fontFamily="@font/inter"
                            android:text="Accurate financial reports"
                            android:textColor="#000000"
                            android:textFontWeight="400"
                            android:textSize="14sp" />

                    </LinearLayout>


                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnTrail"
                android:layout_width="match_parent"
                android:layout_height="54dp"
                android:paddingVertical="0dp"
                android:layout_marginTop="20dp"
                android:fontFamily="@font/inter"
                android:paddingHorizontal="2dp"
                android:text="Start 14-Day Free Trail"
                android:textColor="@color/white"
                android:textFontWeight="600"
                android:textSize="16sp"
                app:backgroundTint="@color/black"
                app:cornerRadius="8dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/dialogCardView" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </RelativeLayout>




</androidx.constraintlayout.widget.ConstraintLayout>
    