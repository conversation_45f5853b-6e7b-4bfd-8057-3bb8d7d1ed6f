<?xml version="1.0" encoding="utf-8"?>
    
    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingHorizontal="5dp"
        tools:ignore="MissingDefaultResource">

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btnAddDraw"
        android:layout_width="match_parent"
        android:layout_height="54dp"
        android:paddingVertical="0dp"
        android:text="+ Add Draw"
        android:textColor="#375DFB"
        android:layout_marginTop="10dp"
        app:backgroundTint="@color/white"
        android:textSize="16sp"
        android:fontFamily="@font/inter"
        android:textFontWeight="600"
        app:cornerRadius="8dp"
        app:layout_constraintStart_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:strokeColor="#375DFB"
        app:strokeWidth="2dp" />

    <LinearLayout
        android:id="@+id/line1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginTop="10dp"
        android:paddingHorizontal="10dp"
        android:paddingVertical="10dp"
        android:background="@drawable/rounded_edittext"




        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/btnAddDraw"

        >


        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Emplyee #1 (Owner)"
                android:textSize="14sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="500"
                android:layout_centerVertical="true"
                android:textColor="#000000"/>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_centerVertical="true"
                android:layout_alignParentEnd="true">


                <ImageView
                    android:id="@+id/btnEdit"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="1dp"
                    android:src="@drawable/edit" />

                <ImageView
                    android:id="@+id/btnDelete"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_marginRight="5dp"
                    android:src="@drawable/delete" />
            </LinearLayout>





        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Amount"
                android:layout_centerVertical="true"
                android:textSize="14sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="400"
                android:textColor="#525866"/>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="10dp"
                android:orientation="horizontal"
                android:gravity="right"
                android:layout_alignParentEnd="true">

                <TextView
                    android:id="@+id/txtAmount"
                    android:textAlignment="textEnd"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="$30.00"
                    android:textSize="14sp"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="500"
                    android:textColor="#000000"/>

                <TextView
                    android:id="@+id/txtPercentage"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Amount"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="5dp"
                    android:textSize="14sp"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="400"
                    android:textColor="#525866"/>


            </LinearLayout>

        </RelativeLayout>

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="10dp"
            android:layout_marginBottom="10dp"
            android:orientation="vertical"
            >

            <TextView
                android:id="@+id/txtRemaing"
                android:textAlignment="textEnd"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="$21.00 Remaining"
                android:layout_alignParentEnd="true"
                android:textSize="12sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="500"
                android:textColor="#868C98"/>


        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Description"
                android:textSize="14sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="500"
                android:layout_centerVertical="true"
                android:textColor="@color/profit_black"/>

        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp">

            <TextView
                android:id="@+id/txtDescription"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Material Cost"
                android:layout_centerVertical="true"
                android:textSize="14sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="400"
                android:textColor="#525866"/>



        </RelativeLayout>



    </LinearLayout>



</androidx.constraintlayout.widget.ConstraintLayout>
    
    