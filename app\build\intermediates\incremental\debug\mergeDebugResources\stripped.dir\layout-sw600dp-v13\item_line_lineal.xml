<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="2dp"
    android:layout_marginRight="2dp"
    app:cardCornerRadius="8dp"
    android:background="@drawable/rounded_edittext"
    android:layout_marginTop="10dp"
    android:layout_marginBottom="10dp">


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingHorizontal="12dp">



        <RadioButton
            android:id="@+id/radioButtonMaterial"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            android:buttonTint="@color/brand_green"
            app:layout_constraintTop_toTopOf="parent"/>


        <TextView
            android:id="@+id/txtLinealItemName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="Material Name"
            android:textSize="14sp"
            android:fontFamily="@font/inter"
            android:textFontWeight="400"
            android:textColor="#525866"
            android:layout_marginTop="8dp"
            app:layout_constraintStart_toEndOf="@id/radioButtonMaterial"
            app:layout_constraintBaseline_toBaselineOf="@id/radioButtonMaterial"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>


        <TextView
            android:id="@+id/txtUnitStart"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="*"
            android:textSize="14sp"
            android:fontFamily="@font/inter"
            android:textFontWeight="500"
            android:textColor="#DF1C41"
            android:layout_marginTop="5dp"
            app:layout_constraintStart_toStartOf="@id/txtLinealItemName"
            app:layout_constraintTop_toBottomOf="@id/txtLinealItemName"/>

        <TextView
            android:id="@+id/txtUnit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Linear Foot"
            android:textSize="14sp"
            android:fontFamily="@font/inter"
            android:textFontWeight="500"
            android:textColor="@color/profit_black"
            android:layout_marginTop="5dp"
            android:layout_marginLeft="1dp"
            app:layout_constraintStart_toEndOf=" @+id/txtUnitStart"
            app:layout_constraintTop_toBottomOf="@id/txtLinealItemName"/>


        <EditText
            android:id="@+id/edtUnits"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:inputType="text"
            android:digits="0123456789 "
            android:maxLines="1"
            android:textSize="14sp"
            android:fontFamily="@font/inter"
            android:textFontWeight="400"
            android:padding="12dp"
            android:layout_marginTop="2dp"
            android:background="@drawable/rounded_edittext"
            app:layout_constraintStart_toStartOf="@id/txtLinealItemName"
            app:layout_constraintTop_toBottomOf="@id/txtUnit"
            app:layout_constraintEnd_toEndOf="parent"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toEndOf=" @+id/txtUnitStart"
            app:layout_constraintTop_toBottomOf="@id/edtUnits"/>


    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView>
