<?xml version="1.0" encoding="utf-8"?>
    
    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingVertical="10dp"
        tools:ignore="MissingDefaultResource">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingHorizontal="10dp"
        android:paddingVertical="10dp"
                android:background="@drawable/rounded_edittext"
        app:layout_constraintStart_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">


        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Emplyee #1 (Owner)"
                android:textSize="14sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="500"

                android:layout_centerVertical="true"
                android:textColor="#000000"/>

            <ImageView
                android:id="@+id/btnDelete"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_centerVertical="true"
                android:layout_alignParentEnd="true"
                android:layout_marginRight="5dp"
                android:src="@drawable/delete" />

            <ImageView
                android:id="@+id/btnEdit"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_centerVertical="true"
                android:layout_toStartOf="@+id/btnDelete"
                android:layout_marginRight="1dp"
                android:src="@drawable/edit" />



        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Name"
                android:textSize="14sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="400"
                android:layout_centerVertical="true"
                android:textColor="#525866"/>

            <TextView
                android:id="@+id/txtMName"
                android:textAlignment="textEnd"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="10dp"
                android:text="Kapil Kumar"
                android:textSize="14sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="500"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:textColor="#000000"/>

        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="10dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Unit Cost"
                android:textSize="14sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="400"
                android:layout_centerVertical="true"
                android:textColor="#525866"/>

            <TextView
                android:id="@+id/txtUnitCost"
                android:textAlignment="textEnd"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="10dp"
                android:text="$30.00"
                android:textSize="14sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="500"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:textColor="#000000"/>

        </RelativeLayout>



    </LinearLayout>



    
    </androidx.constraintlayout.widget.ConstraintLayout>
    
    