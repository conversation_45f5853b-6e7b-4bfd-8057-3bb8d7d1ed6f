<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_round_corners"
    android:orientation="vertical"
    android:paddingVertical="@dimen/_10sdp"
    android:fitsSystemWindows="true"
    android:layout_marginBottom="0dp"
    app:layout_behavior="@string/bottom_sheet_behavior">


    <LinearLayout
        android:id="@+id/header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingHorizontal="@dimen/_15sdp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:paddingTop="0dp">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_marginTop="@dimen/_5sdp"
            android:orientation="horizontal">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                >

                <TextView
                    android:id="@+id/addHeading"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Add Employee"
                    android:textColor="#0A0D14"
                    android:textSize="16sp"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="500"
                    android:layout_centerHorizontal="true"
                    android:layout_centerVertical="true"
                    android:gravity="center" />
            </RelativeLayout>

            <ImageView
                android:id="@+id/backButton"
                android:layout_width="@dimen/_25sdp"
                android:layout_height="@dimen/_25sdp"
                android:layout_centerVertical="true"
                android:src="@drawable/cross" />





            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnSave"
                android:layout_width="wrap_content"
                android:layout_height="48sp"
                android:paddingVertical="0dp"
                android:layout_alignParentEnd="true"
                android:textColor="#375DFB"
                android:textSize="16sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="500"
                app:cornerRadius="8dp"
                app:strokeWidth="2dp"
                app:strokeColor="#375DFB"
                app:backgroundTint="@color/white"
                android:text="Save" />
        </RelativeLayout>

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="10dp"
        android:background="#D3D3D3"
        />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        android:id="@+id/mainLayout">


        <LinearLayout
            android:id="@+id/line1"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/app_bg_color"
            android:orientation="vertical"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/headerInclude">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_15sdp"
                android:text="Full Name"
                android:textSize="14sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="500"
                android:layout_marginHorizontal="@dimen/_15sdp"
                android:textColor="#0A0D14"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/headerInclude" />

            <EditText
                android:id="@+id/edTxtFullName"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                app:layout_constraintEnd_toEndOf="parent"
                android:layout_marginTop="@dimen/_2sdp"
                android:inputType="text"
                android:digits="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ "
                android:maxLines="1"
                android:textSize="14sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="400"
                android:padding="12dp"
                android:background="@drawable/rounded_edittext"
                android:layout_marginHorizontal="@dimen/_15sdp"

                />

            <TextView
                android:id="@+id/tvFullName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_15sdp"
                android:text="Hourly Rate"
                android:textSize="14sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="500"
                android:layout_marginHorizontal="@dimen/_15sdp"
                android:textColor="#0A0D14"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/headerInclude" />

            <LinearLayout
                android:id="@+id/linearDefaultHourlyRate2"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:background="@drawable/rounded_edittext"
                android:layout_marginHorizontal="@dimen/_15sdp"
                android:layout_marginTop="@dimen/_2sdp"
                android:padding="6dp"
                android:orientation="horizontal"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/linearDefaultHourlyRate"
                >

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="$"
                    android:textSize="14sp"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="400"
                    android:layout_gravity="center"
                    android:textColor="#868C98"
                    android:paddingLeft="@dimen/_4sdp"
                    android:paddingRight="@dimen/_2sdp"
                    />

                <EditText
                    android:id="@+id/edTxtHourlyRate"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_margin="@dimen/_3sdp"
                    android:textSize="14sp"
                    android:fontFamily="@font/inter"
                    android:digits="1234567890."
                    android:textFontWeight="400"
                    android:layout_gravity="center"
                    android:inputType="number"
                    android:maxLines="1"
                    android:background="@drawable/rounded_edittext2"
                    />
            </LinearLayout>

        </LinearLayout>
    </ScrollView>




</LinearLayout>