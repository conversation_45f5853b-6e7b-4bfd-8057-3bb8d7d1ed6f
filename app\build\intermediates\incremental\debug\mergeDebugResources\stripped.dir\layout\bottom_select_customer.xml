<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_round_corners"
    android:orientation="vertical"
    android:layout_marginTop="@dimen/_90sdp"

    android:paddingVertical="@dimen/_10sdp"
    app:layout_behavior="@string/bottom_sheet_behavior">


    <LinearLayout
        android:id="@+id/header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingHorizontal="@dimen/_15sdp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:paddingTop="0dp">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_marginTop="@dimen/_5sdp"
            android:orientation="horizontal">


            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                >

                <TextView
                    android:id="@+id/addHeading"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Select Customer"
                    android:textSize="16sp"
                    android:textColor="#0A0D14"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="500"
                    android:layout_centerHorizontal="true"
                    android:layout_centerVertical="true"
                    android:gravity="center" />
            </RelativeLayout>

            <ImageView
                android:id="@+id/backButton"
                android:layout_width="@dimen/_25sdp"
                android:layout_centerVertical="true"
                android:layout_height="@dimen/_25sdp"
                android:src="@drawable/cross" />



            <com.google.android.material.button.MaterialButton
                android:id="@+id/createCustomer"
                android:layout_width="wrap_content"
                android:layout_height="48sp"
                android:paddingVertical="0dp"
                android:layout_alignParentEnd="true"
                android:textColor="#375DFB"
                android:textSize="16sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="500"
                app:cornerRadius="8dp"
                app:strokeWidth="2dp"
                app:strokeColor="#375DFB"
                app:backgroundTint="@color/white"

                android:text="New" />


        </RelativeLayout>

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="10dp"
        android:background="#D3D3D3"
        />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:layout_marginTop="@dimen/_16sdp"
        android:layout_marginHorizontal="@dimen/_15sdp"
        >

        <EditText
            android:id="@+id/edTxtMaterialName"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginTop="@dimen/_2sdp"
            android:inputType="text"
            android:hint="Search Customer"
            android:digits="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ "
            android:textSize="14sp"
            android:fontFamily="@font/inter"
            android:textFontWeight="400"
            android:maxLines="1"
            android:padding="12dp"
            android:background="@drawable/rounded_edittext"


            />


        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/employeeRecylerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="@dimen/_19sdp"
            android:paddingBottom="20dp"
            app:layoutManager="LinearLayoutManager"

            />

        <RelativeLayout
            android:id="@+id/noCustomer"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
           >

            <TextView
                android:id="@+id/noMatch"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="no matches..."
                android:textSize="16sp"
                android:textColor="#525866"
                android:fontFamily="@font/inter"
                android:textFontWeight="400"
                android:layout_marginTop="@dimen/_15sdp"
                android:gravity="center" />



        </RelativeLayout>




    </LinearLayout>


</LinearLayout>