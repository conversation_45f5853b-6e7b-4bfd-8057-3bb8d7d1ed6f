<?xml version="1.0" encoding="utf-8"?>
    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        app:layout_behavior="@string/bottom_sheet_behavior">
    
        <TextView
            android:id="@+id/textView29"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="16dp"
            android:textStyle="bold"
            android:text="Update profile picture"
            android:textColor="@color/black"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    
        <View
            android:id="@+id/view"
            android:layout_width="match_parent"
            android:layout_height="2dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/textView29" />
    
        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/btnGallery"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:layout_marginTop="20dp"
            android:drawableStart="@android:drawable/ic_menu_gallery"
            android:drawablePadding="15dp"
            android:padding="16dp"
            android:text="Upload image"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/view" />
    
        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/btnCamera"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:drawableStart="@android:drawable/ic_menu_camera"
            android:drawablePadding="15dp"
            android:padding="16dp"
            android:text="Take a photo"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/btnGallery" />
    
        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/imageView6"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:drawableStart="@android:drawable/ic_menu_delete"
            android:drawablePadding="15dp"
            android:padding="16dp"
            android:layout_marginBottom="10dp"
            android:text="Remove"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/btnCamera" />
    
    </androidx.constraintlayout.widget.ConstraintLayout>
    