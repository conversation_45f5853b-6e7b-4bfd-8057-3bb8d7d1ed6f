<?xml version="1.0" encoding="utf-8"?>
    <FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@android:color/transparent">
    
        <androidx.cardview.widget.CardView
            android:id="@+id/dialogCardView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/_15sdp"
            app:cardBackgroundColor="@color/white"
            app:cardCornerRadius="@dimen/_26sdp"
            app:cardElevation="0dp">
    
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="vertical"
                android:paddingHorizontal="@dimen/_15sdp"
                android:paddingBottom="@dimen/_20sdp">
    
                <ImageView
                    android:id="@+id/imageViewAlertDialog"
                    android:layout_width="@dimen/_28sdp"
                    android:layout_height="@dimen/_28sdp"
                    android:layout_gravity="end"
                    android:layout_marginTop="@dimen/_10sdp"
                    android:src="@android:drawable/ic_menu_info_details"
                    tools:ignore="ContentDescription" />
    
    
    
                <TextView
                    android:id="@+id/tvAlertDialogMsg"
                    style="@style/h2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/_5sdp"
                    android:layout_marginTop="@dimen/_10sdp"
                    android:text="Notification" />
    
          
    
    
                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/buttonAlertDialogClose"
                    android:layout_marginTop="@dimen/_20sdp"
                    android:text="Close"
                    android:layout_marginBottom="@dimen/_40sdp"
                    android:textColor="@color/white"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.5"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvAlertDialogMsg"  
                    android:layout_height="wrap_content"
                    android:layout_width="wrap_content"/>
    
            </LinearLayout>
    
        </androidx.cardview.widget.CardView>
    
    </FrameLayout>
    