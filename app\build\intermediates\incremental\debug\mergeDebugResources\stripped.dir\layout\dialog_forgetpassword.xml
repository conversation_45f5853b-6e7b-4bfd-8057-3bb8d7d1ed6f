<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent">

    <androidx.cardview.widget.CardView
        android:id="@+id/dialogCardView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_15sdp"
        app:cardBackgroundColor="@color/white"
        app:cardCornerRadius="@dimen/_16sdp"
        app:cardElevation="1dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"

            android:paddingBottom="@dimen/_10sdp">


            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_15sdp"
                android:text="Email sent!"
                android:paddingHorizontal="@dimen/_15sdp"
                android:textSize="16sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="500"
                android:textColor="#0A0D14" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_5sdp"
                android:paddingHorizontal="@dimen/_15sdp"
                android:text="You should receive an email at:"
                android:textSize="14sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="400"
                android:textColor="#525866" />

            <TextView
                android:id="@+id/email"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingHorizontal="@dimen/_15sdp"
                android:text="You should receive an email at:"
                android:textSize="14sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="500"
                android:textColor="#0A0D14" />


            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_15sdp"
                android:paddingHorizontal="@dimen/_15sdp"
                android:text="(Remember to check your spam folder!)"
                android:textSize="14sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="400"
                android:textColor="#525866" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_15sdp"
                android:paddingHorizontal="@dimen/_15sdp"
                android:text="Didn’t get the email?"
                android:textSize="14sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="400"
                android:textColor="#525866" />

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_10sdp"
                android:paddingHorizontal="@dimen/_15sdp">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnSendAgain"
                    android:layout_width="wrap_content"
                    android:layout_height="40dp"
                    android:paddingVertical="0sp"
                    android:layout_alignParentStart="true"
                    android:textColor="#375DFB"
                    android:textSize="16sp"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="500"
                    app:cornerRadius="8dp"
                    app:strokeWidth="2dp"
                    app:strokeColor="#375DFB"
                    app:backgroundTint="@color/white"
                    android:text="Resend" />

            </RelativeLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/_1sdp"
                android:layout_marginTop="@dimen/_10sdp"
                android:background="#E2E4E9"/>
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_5sdp"
                >

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnResetPassword"
                    android:layout_width="wrap_content"
                    android:layout_height="48sp"
                    android:paddingVertical="0dp"
                    android:layout_marginTop="@dimen/_10sdp"
                    android:layout_marginRight="19sp"
                    android:textSize="14sp"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="500"
                    android:layout_alignParentEnd="true"
                    android:textColor="@color/white"
                    app:cornerRadius="8dp"
                    app:backgroundTint="@color/black"
                    android:text="Set New Password" />

            </RelativeLayout>
        </LinearLayout>



    </androidx.cardview.widget.CardView>

</FrameLayout>