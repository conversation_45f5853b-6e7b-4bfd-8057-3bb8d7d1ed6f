<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:animateLayoutChanges="true"
        
        android:orientation="vertical"
    android:background="@color/app_bg_color"
    >

    <include
        android:id="@+id/headerInclude"
        layout="@layout/header"

        ></include>




    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_20sdp"
        android:layout_marginTop="@dimen/_30sdp"
        android:layout_below="@+id/headerInclude"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tvUserName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="16sp"
            android:fontFamily="@font/inter"
            android:textFontWeight="500"
            android:textColor="#000"
            android:text="UserName" />

        <TextView
            android:id="@+id/tvUserEmail"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="12sp"
            android:fontFamily="@font/inter"
            android:textFontWeight="400"
            android:textColor="#3C3C43"
            android:alpha="0.8"
            android:text="UserName" />

        <LinearLayout
            android:id="@+id/line1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_20sdp"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="@dimen/_20sdp"
                android:layout_height="@dimen/_20sdp"
                android:layout_gravity="center"
                android:src="@drawable/user_icon"/>

            <TextView
                android:layout_marginLeft="@dimen/_8sdp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:textSize="14sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="400"
                android:textColor="#000"
                android:text="Profile" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/line2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_15sdp"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="@dimen/_20sdp"
                android:layout_height="@dimen/_20sdp"
                android:layout_gravity="center"
                android:src="@drawable/setting_icon"/>

            <TextView
                android:layout_marginLeft="@dimen/_8sdp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:textSize="14sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="400"
                android:textColor="#000"
                android:text="Settings" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/line3"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_15sdp"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="@dimen/_20sdp"
                android:layout_height="@dimen/_20sdp"
                android:layout_gravity="center"
                android:src="@drawable/subscription_icon"/>

            <TextView
                android:layout_marginLeft="@dimen/_8sdp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:textSize="14sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="400"
                android:textColor="#000"
                android:text="Plan and Billing" />

        </LinearLayout>


        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/_1sdp"
            android:layout_marginTop="@dimen/_15sdp"
            android:layout_marginBottom="@dimen/_15sdp"
            android:background="#E2E4E9"/>

        <LinearLayout
            android:id="@+id/line4"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_10sdp"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="@dimen/_20sdp"
                android:layout_height="@dimen/_20sdp"
                android:layout_gravity="center"
                android:src="@drawable/logout_icon"/>

            <TextView
                android:layout_marginLeft="@dimen/_8sdp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:textSize="14sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="400"
                android:textColor="#000"
                android:text="Log out" />

        </LinearLayout>
    </LinearLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="@dimen/_40sdp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginBottom="@dimen/_20sdp"
            android:layout_marginHorizontal="@dimen/_20sdp"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="25dp"
                    android:textSize="12sp"
                    android:textAlignment="textStart"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="400"
                    android:textColor="#3C3C43"
                    android:alpha="0.8"
                    android:layout_weight="1"
                    android:text="Terms and Conditions" />

                <TextView
                    android:layout_marginLeft="@dimen/_8sdp"
                    android:layout_width="wrap_content"
                    android:layout_height="25dp"
                    android:textSize="12sp"
                    android:textAlignment="textEnd"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="400"
                    android:textColor="#3C3C43"
                    android:alpha="0.8"
                    android:layout_weight="1"
                    android:text="Privacy Policy" />

            </LinearLayout>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="12sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="400"
                android:textColor="#3C3C43"
                android:alpha="0.8"
                android:layout_weight="1"
                android:text="v.1.20" />
        </LinearLayout>


    </RelativeLayout>

</LinearLayout>
