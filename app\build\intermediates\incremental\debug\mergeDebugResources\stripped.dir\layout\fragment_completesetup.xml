<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/app_bg_color">


    <include
        android:id="@+id/headerInclude"
        layout="@layout/header"

        ></include>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginTop="@dimen/_60sdp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/headerInclude"
        >

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">



            <LinearLayout
                android:id="@+id/linearDefaultHourlyRate3"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/rounded_edittext"
                android:layout_marginHorizontal="@dimen/_20sdp"
                android:padding="15dp"
                android:layout_marginTop="@dimen/_15sdp"
                android:orientation="vertical"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/linearDefaultHourlyRate2"
                >

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <ImageView
                        android:layout_width="@dimen/_24sdp"
                        android:layout_height="@dimen/_24sdp"
                        android:layout_gravity="center"
                        android:src="@drawable/green_check"/>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginLeft="@dimen/_10sdp"
                        android:text="You’re all set!"
                        android:textColor="#000000"
                        android:textSize="14sp"
                        android:fontFamily="@font/inter"
                        android:textFontWeight="500"
                        />
                </LinearLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_10sdp"
                    android:textSize="14sp"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="400"
                    android:text="Account has been successfully set up."
                    android:textColor="#525866"
                    />


            </LinearLayout>


            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnContinue"
                android:layout_width="match_parent"
                android:layout_height="54dp"
                android:layout_marginHorizontal="@dimen/_20sdp"
                android:layout_marginTop="@dimen/_10sdp"
                android:fontFamily="@font/inter"
                android:paddingHorizontal="@dimen/_48sdp"
                android:paddingVertical="0dp"
                android:text="Finish"
                android:textColor="@color/black"
                android:textFontWeight="600"
                android:textSize="16sp"
                app:backgroundTint="@color/brand_green"
                app:cornerRadius="8dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/linearDefaultProfitOverhead3" />
        </LinearLayout>

    </ScrollView>







</androidx.constraintlayout.widget.ConstraintLayout>
