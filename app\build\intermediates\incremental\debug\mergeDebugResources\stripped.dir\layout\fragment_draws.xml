<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/app_bg_color"
    android:id="@+id/coss"
    tools:context="com.manaknight.app.ui.fragments.LoginFragment">


    <include
        android:id="@+id/headerInclude"
        layout="@layout/header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>

    <LinearLayout
        android:id="@+id/line1"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="@dimen/_55sdp"
        android:paddingHorizontal="@dimen/_16sdp"
        app:layout_constraintTop_toBottomOf="@id/headerInclude"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingHorizontal="@dimen/_10sdp"
            android:paddingVertical="@dimen/_10sdp"
            android:background="@drawable/rounded_edittext"
            app:layout_constraintStart_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent">


            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Sale Price"
                    android:textSize="14sp"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="400"
                    android:layout_centerVertical="true"
                    android:textColor="#525866"/>

                <TextView
                    android:id="@+id/txtSalePrice"
                    android:textAlignment="textEnd"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="@dimen/_10sdp"
                    android:text="Kapil Kumar"
                    android:textSize="14sp"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="500"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:textColor="#000000"/>

            </RelativeLayout>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_10sdp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Unaccounted For"
                    android:textSize="14sp"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="400"
                    android:layout_centerVertical="true"
                    android:textColor="#525866"/>

                <TextView
                    android:id="@+id/txtUnaccounted"
                    android:textAlignment="textEnd"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="@dimen/_10sdp"
                    android:text="Kapil Kumar"
                    android:textSize="14sp"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="500"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:textColor="#000000"/>

            </RelativeLayout>



        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/materialRecylerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="@dimen/_16sdp"
            android:paddingBottom="20dp"
            app:layoutManager="LinearLayoutManager" />

    </LinearLayout>



</androidx.constraintlayout.widget.ConstraintLayout>
