<?xml version="1.0" encoding="utf-8"?>
    
        <RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:id="@+id/relativeLayout"
    android:layout_height="match_parent"
    android:background="@color/app_bg_color"
    android:orientation="vertical">


        <include
            android:id="@+id/headerInclude"
            layout="@layout/header"

            ></include>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_20sdp"
            android:layout_marginTop="@dimen/_30sdp"
            android:layout_below="@+id/headerInclude"
            android:orientation="vertical">

                <TextView
                    android:id="@+id/textView2"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textSize="14sp"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="400"
                    android:textColor="#525866"
                    android:text="We will email you a link to reset your password." />


                <TextView
                    android:id="@+id/tvEmail"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Email"
                    android:textSize="14sp"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="500"
                    android:layout_marginTop="@dimen/_15sdp"
                    android:textColor="#0A0D14"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/headerInclude" />

                <EditText
                    android:id="@+id/edTxtForgetPassword"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_marginTop="@dimen/_2sdp"
                    app:layout_constraintEnd_toEndOf="parent"
                    android:inputType="textEmailAddress"
                    android:textSize="14sp"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="400"
                    android:maxLines="1"
                    android:paddingHorizontal="12dp"
                    android:background="@drawable/rounded_edittext"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvEmail"
                    />




                <com.google.android.material.button.MaterialButton
                    android:id="@+id/continueButton"
                    android:layout_width="match_parent"
                    android:layout_height="54dp"
                    android:paddingVertical="0dp"
                    android:layout_marginTop="@dimen/_15sdp"
                    android:paddingHorizontal="@dimen/_48sdp"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="600"
                    app:cornerRadius="8dp"
                    app:backgroundTint="@color/black"
                    android:text="Request Link" />
        </LinearLayout>

</RelativeLayout>

    