<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/app_bg_color"
    android:id="@+id/coss"
    tools:context="com.manaknight.app.ui.fragments.LoginFragment">


    <include
        android:id="@+id/headerInclude"
        layout="@layout/header"

        />

    <LinearLayout
        android:id="@+id/topLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginTop="@dimen/_1sdp"
        android:layout_marginHorizontal="@dimen/_16sdp"
        app:layout_constraintTop_toBottomOf="@id/headerInclude"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:paddingTop="0dp">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/_40sdp"
            android:layout_marginTop="@dimen/_10sdp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Linear Foot Costs"
                android:textSize="15sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="500"
                android:textColor="#525866"
                android:layout_centerVertical="true"
                android:gravity="center" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnAddLineItem"
                android:layout_width="wrap_content"
                android:layout_height="48sp"
                android:paddingVertical="0dp"
                android:layout_centerVertical="true"
                android:layout_alignParentEnd="true"
                android:textColor="#375DFB"
                android:textSize="16sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="500"
                app:cornerRadius="8dp"
                app:strokeWidth="2dp"
                app:strokeColor="#375DFB"
                app:backgroundTint="@color/white"
                android:text="Add New Cost" />

        </RelativeLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/line1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"

        android:orientation="vertical"
        android:layout_marginTop="@dimen/_1sdp"
        android:layout_marginBottom="@dimen/_20sdp"
        android:layout_marginHorizontal="@dimen/_16sdp"
        app:layout_constraintTop_toBottomOf="@id/topLayout"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/linealRecylerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="@dimen/_10sdp"
            android:paddingBottom="20dp"

            app:layoutManager="LinearLayoutManager" />
    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>
