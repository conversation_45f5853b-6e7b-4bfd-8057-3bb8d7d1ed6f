<?xml version="1.0" encoding="utf-8"?>
   <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
    android:background="@color/app_bg_color"
    android:id="@+id/constraint"
        tools:context="com.manaknight.app.ui.fragments.LoginFragment">


    <include
        android:id="@+id/headerInclude"
        layout="@layout/header"

        ></include>

    <TextView
        android:id="@+id/tvEmail"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_22sdp"
        android:text="Email"
        android:textSize="14sp"
        android:fontFamily="@font/inter"
        android:textFontWeight="500"
        android:layout_marginHorizontal="@dimen/_20sdp"
        android:textColor="#0A0D14"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/headerInclude" />

    <EditText
        android:id="@+id/edTxtUserName"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:layout_marginTop="@dimen/_2sdp"
        app:layout_constraintEnd_toEndOf="parent"
        android:inputType="textEmailAddress"
        android:maxLines="1"
        android:textSize="14sp"
        android:fontFamily="@font/inter"
        android:textFontWeight="400"
        android:paddingHorizontal="12dp"
        android:background="@drawable/rounded_edittext"
        android:layout_marginHorizontal="@dimen/_20sdp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvEmail"
        />


    <TextView
        android:id="@+id/tvPassword"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_15sdp"
        android:text="Password"
        android:textSize="14sp"
        android:fontFamily="@font/inter"
        android:textFontWeight="500"
        android:layout_marginHorizontal="@dimen/_20sdp"
        android:textColor="#0A0D14"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/edTxtUserName" />

    <EditText
        android:id="@+id/edTxtPassword"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="@dimen/_2sdp"
        android:inputType="textPassword"
        android:maxLines="1"
        android:textSize="14sp"
        android:fontFamily="@font/inter"
        android:textFontWeight="400"
        android:paddingHorizontal="12dp"
        android:background="@drawable/rounded_edittext"
        android:layout_marginHorizontal="@dimen/_20sdp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvPassword"
        />


    <TextView
        android:id="@+id/btnForgetPassword"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_5sdp"
        android:layout_marginStart="19sp"
        android:text="@string/forget_password"
        android:textSize="14sp"
        android:textColor="#0A0D14"
        android:fontFamily="@font/inter"
        android:textFontWeight="500"
        app:layout_constraintEnd_toEndOf="@+id/edTxtPassword"
        app:layout_constraintTop_toBottomOf="@+id/edTxtPassword"
        app:layout_constraintVertical_bias="0.0" />

        <com.google.android.material.button.MaterialButton
                android:id="@+id/btnLogin"
                android:layout_width="match_parent"
                android:layout_height="54dp"
            android:paddingVertical="0dp"
                android:layout_marginTop="@dimen/_15sdp"
                android:paddingHorizontal="@dimen/_48sdp"
            android:layout_marginHorizontal="@dimen/_20sdp"
            android:textColor="@color/white"
            android:textSize="16sp"
            android:fontFamily="@font/inter"
            android:textFontWeight="600"
            app:cornerRadius="8dp"
            app:backgroundTint="@color/black"
            android:text="Sign In"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/btnForgetPassword" />



            <TextView
                android:id="@+id/btnSignup"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_10sdp"
                android:text="@string/don_t_have_an_account"
                android:textColor="#525866"
                android:textSize="14sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="400"
                android:layout_marginHorizontal="@dimen/_20sdp"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/btnLogin"
                android:visibility="visible"/>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="20sp"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <TextView
            android:id="@+id/btnTerms"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/inter"
            android:text="Terms and Conditions"
            android:textColor="#868C98"
            android:textFontWeight="500"
            android:textSize="12sp" />

        <TextView
            android:id="@+id/btnPolicy"
            android:layout_marginLeft="20sp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Privacy Policy"
            android:textSize="12sp"
            android:fontFamily="@font/inter"
            android:textFontWeight="500"
            android:textColor="#868C98" />

    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>
