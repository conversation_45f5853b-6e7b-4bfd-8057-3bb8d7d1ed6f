<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/app_bg_color">


    <include
        android:id="@+id/headerInclude"
        layout="@layout/header"

        ></include>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginTop="@dimen/_60sdp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/headerInclude"
        >

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">



            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/materialRecylerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_5sdp"
                android:layout_marginHorizontal="@dimen/_20sdp"
                android:paddingBottom="20dp"
                app:layoutManager="LinearLayoutManager" />


            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnAddMaterial"
                android:layout_width="match_parent"
                android:layout_height="54dp"
                android:paddingVertical="0dp"
                android:layout_marginHorizontal="@dimen/_20sdp"
                android:layout_marginTop="@dimen/_15sdp"
                android:paddingHorizontal="@dimen/_48sdp"
                android:text="+ Add Material"
                android:textColor="#375DFB"
                app:backgroundTint="@color/white"
                android:textSize="16sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="600"

                app:cornerRadius="8dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/linearDefaultProfitOverhead3"
                app:strokeColor="#375DFB"
                app:strokeWidth="2dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnContinue"
                android:layout_width="match_parent"
                android:layout_height="54dp"
                android:paddingVertical="0dp"
                android:layout_marginTop="@dimen/_10sdp"
                android:paddingHorizontal="@dimen/_48sdp"
                android:layout_marginHorizontal="@dimen/_20sdp"
                android:textColor="@color/white"
                android:textSize="16sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="600"

                app:cornerRadius="8dp"
                app:backgroundTint="@color/black"
                android:text="Continue"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/linearDefaultProfitOverhead3" />
        </LinearLayout>

    </ScrollView>







</androidx.constraintlayout.widget.ConstraintLayout>
