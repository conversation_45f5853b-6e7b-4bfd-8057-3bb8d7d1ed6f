<?xml version="1.0" encoding="utf-8"?>
  <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
      xmlns:app="http://schemas.android.com/apk/res-auto"
      xmlns:tools="http://schemas.android.com/tools"
      android:layout_width="match_parent"
      android:layout_height="match_parent"
      tools:context="com.manaknight.app.ui.fragments.ProfileFragment">
  
  
  
      <androidx.core.widget.NestedScrollView
          android:layout_width="match_parent"
          android:layout_height="0dp"
          app:layout_constraintBottom_toBottomOf="parent"
          app:layout_constraintEnd_toEndOf="parent"
          app:layout_constraintHorizontal_bias="0.5"
          app:layout_constraintStart_toStartOf="parent"
          app:layout_constraintTop_toTopOf="parent"
          app:layout_constraintVertical_bias="0.5">
  
          <androidx.constraintlayout.widget.ConstraintLayout
              android:layout_width="match_parent"
              android:layout_height="wrap_content"
              app:layout_constraintTop_toTopOf="parent">
  
              <androidx.cardview.widget.CardView
                  android:id="@+id/materialCardView2"
                  android:layout_width="100dp"
                  android:layout_height="100dp"
                  android:layout_marginTop="10dp"
                  app:cardCornerRadius="70dp"
                  app:layout_constraintEnd_toEndOf="parent"
                  app:layout_constraintStart_toStartOf="parent"
                  app:layout_constraintTop_toTopOf="parent">
  
                  <ImageView
                      android:id="@+id/ivUser"
                      android:layout_width="100dp"
                      android:layout_height="100dp"
                      android:scaleType="centerCrop"
                      android:src="@android:drawable/ic_menu_gallery" />
  
              </androidx.cardview.widget.CardView>
  
              <ImageView
                  android:id="@+id/ivEdit"
                  android:layout_width="40dp"
                  android:layout_height="40dp"
                  android:elevation="8dp"
                  android:src="@android:drawable/ic_menu_camera"
                  app:layout_constraintBottom_toBottomOf="@+id/materialCardView2"
                  app:layout_constraintEnd_toEndOf="@+id/materialCardView2" />
  
  
              <EditText
                  android:id="@+id/editTextFirstName"
                  android:layout_width="match_parent"
                  android:layout_height="wrap_content"
                  android:layout_marginTop="15dp"
                  android:padding="16dp"
                  app:layout_constraintTop_toBottomOf="@id/ivEdit"
                  android:hint="First Name"
                  />
  
              <EditText
                  android:id="@+id/editTextLastName"
                  android:layout_width="match_parent"
                  android:layout_height="wrap_content"
                  android:layout_marginTop="15dp"
                  android:padding="16dp"
                  app:layout_constraintTop_toBottomOf="@id/editTextFirstName"
                  android:hint="Last Name"
                  />
              <com.google.android.material.button.MaterialButton
                  android:id="@+id/buttonSave"
                  android:layout_width="match_parent"
                  android:layout_height="wrap_content"
                  android:layout_marginHorizontal="16dp"
                  android:paddingVertical="0dp"
                  android:layout_marginTop="15dp"
                  android:padding="16dp"
                  android:text="Save"
                  app:cornerRadius="10dp"
                  app:layout_constraintEnd_toEndOf="parent"
                  app:layout_constraintTop_toBottomOf="@id/editTextLastName"
                  app:strokeWidth="2dp" />
  
  
          </androidx.constraintlayout.widget.ConstraintLayout>
  
      </androidx.core.widget.NestedScrollView>
  
  </androidx.constraintlayout.widget.ConstraintLayout>
  
  