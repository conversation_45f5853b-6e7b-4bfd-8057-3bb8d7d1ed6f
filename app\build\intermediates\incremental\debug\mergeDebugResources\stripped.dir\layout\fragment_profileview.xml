<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/constraint"
    android:background="@color/app_bg_color">


    <include
        android:id="@+id/headerInclude"
        layout="@layout/header"

        ></include>

    <ScrollView
        android:id="@+id/scrollable"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:focusableInTouchMode="true"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginTop="@dimen/_60sdp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/headerInclude"
        >

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:id="@+id/line1"
            android:orientation="vertical">


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginHorizontal="@dimen/_15sdp"
                android:paddingHorizontal="@dimen/_12sdp"
                android:paddingVertical="@dimen/_10sdp"
                android:background="@drawable/rounded_edittext"
                app:layout_constraintStart_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent">


                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Profile Details"
                        android:textSize="14sp"
                        android:fontFamily="@font/inter"
                        android:textFontWeight="500"
                        android:layout_centerVertical="true"
                        android:textColor="#000000"/>

                    <ImageView
                        android:id="@+id/btnEdit"
                        android:layout_width="@dimen/_32sdp"
                        android:layout_height="@dimen/_32sdp"
                        android:layout_centerVertical="true"
                        android:layout_alignParentEnd="true"
                        android:layout_marginRight="@dimen/_5sdp"
                        android:src="@drawable/edit" />


                </RelativeLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_marginTop="@dimen/_10sdp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="First Name"
                        android:textSize="12sp"
                        android:fontFamily="@font/inter"
                        android:textFontWeight="400"
                        android:layout_centerVertical="true"
                        android:textColor="#525866"/>

                    <TextView
                        android:id="@+id/txtFName"
                        android:textAlignment="textEnd"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Kapil Kumar"
                        android:textSize="14sp"
                        android:fontFamily="@font/inter"
                        android:textFontWeight="500"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:textColor="#000000"/>

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_1sdp"
                    android:layout_marginTop="@dimen/_10sdp"
                    android:layout_marginBottom="@dimen/_10sdp"
                    android:background="#E2E4E9"/>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Last Name"
                        android:textSize="12sp"
                        android:fontFamily="@font/inter"
                        android:textFontWeight="400"
                        android:layout_centerVertical="true"
                        android:textColor="#525866"/>

                    <TextView
                        android:id="@+id/txtLName"
                        android:textAlignment="textEnd"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Kapil Kumar"
                        android:textSize="14sp"
                        android:fontFamily="@font/inter"
                        android:textFontWeight="500"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:textColor="#000000"/>

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_1sdp"
                    android:layout_marginTop="@dimen/_10sdp"
                    android:layout_marginBottom="@dimen/_10sdp"
                    android:background="#E2E4E9"/>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Company Name"
                        android:textSize="12sp"
                        android:fontFamily="@font/inter"
                        android:textFontWeight="400"
                        android:layout_centerVertical="true"
                        android:textColor="#525866"/>

                    <TextView
                        android:id="@+id/txtCompanyName"
                        android:textAlignment="textEnd"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Kapil Kumar"
                        android:textSize="14sp"
                        android:fontFamily="@font/inter"
                        android:textFontWeight="500"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:textColor="#000000"/>

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_1sdp"
                    android:layout_marginTop="@dimen/_10sdp"
                    android:layout_marginBottom="@dimen/_10sdp"
                    android:background="#E2E4E9"/>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Email"
                        android:textSize="12sp"
                        android:fontFamily="@font/inter"
                        android:textFontWeight="400"
                        android:layout_centerVertical="true"
                        android:textColor="#525866"/>

                    <TextView
                        android:id="@+id/txtEmail"
                        android:textAlignment="textEnd"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Kapil Kumar"
                        android:textSize="14sp"
                        android:fontFamily="@font/inter"
                        android:textFontWeight="500"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:textColor="#000000"/>

                </LinearLayout>


            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginTop="@dimen/_20sdp"
                android:layout_marginHorizontal="@dimen/_15sdp"
                android:paddingHorizontal="@dimen/_12sdp"
                android:paddingVertical="@dimen/_10sdp"
                android:background="@drawable/rounded_edittext"
                app:layout_constraintStart_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent">


                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Security"
                        android:textSize="14sp"
                        android:fontFamily="@font/inter"
                        android:textFontWeight="500"
                        android:layout_centerVertical="true"
                        android:textColor="#000000"/>


                </RelativeLayout>


                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnContinue"
                    android:layout_width="match_parent"
                    android:layout_height="54dp"
                    android:paddingVertical="0dp"
                    android:layout_marginTop="@dimen/_10sdp"
                    android:paddingHorizontal="@dimen/_48sdp"
                    android:textColor="@color/white"
                    app:cornerRadius="8dp"
                    app:backgroundTint="@color/black"
                    android:textSize="16sp"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="600"
                    android:text="Update Password"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/linearDefaultProfitOverhead3" />


            </LinearLayout>





        </LinearLayout>

    </ScrollView>







</androidx.constraintlayout.widget.ConstraintLayout>
