<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/app_bg_color"
    android:id="@+id/constraint"
    tools:context="com.manaknight.app.ui.fragments.LoginFragment">


    <include
        android:id="@+id/headerInclude"
        layout="@layout/header"

        ></include>

    <TextView
        android:id="@+id/tvCode"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_30sdp"
        android:text="Code"
        android:textSize="14sp"
        android:fontFamily="@font/inter"
        android:textFontWeight="500"
        android:textColor="#0A0D14"
        android:layout_marginHorizontal="@dimen/_20sdp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/headerInclude" />

    <EditText
        android:id="@+id/edTxtCode"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="@dimen/_2sdp"
        android:inputType="text"
        android:textSize="14sp"
        android:fontFamily="@font/inter"
        android:textFontWeight="400"
        android:maxLines="1"
        android:paddingHorizontal="12dp"
        android:background="@drawable/rounded_edittext"
        android:layout_marginHorizontal="@dimen/_20sdp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvCode"
        />

    <TextView
        android:id="@+id/tvPassword"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_15sdp"
        android:text="Set New Password"
        android:textSize="14sp"
        android:fontFamily="@font/inter"
        android:textFontWeight="500"
        android:textColor="#0A0D14"
        android:layout_marginHorizontal="@dimen/_20sdp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/edTxtCode" />

    <EditText
        android:id="@+id/edTxtPassword"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="@dimen/_2sdp"
        android:inputType="textPassword"
        android:textSize="14sp"
        android:fontFamily="@font/inter"
        android:textFontWeight="400"
        android:maxLines="1"
        android:paddingHorizontal="12dp"
        android:background="@drawable/rounded_edittext"
        android:layout_marginHorizontal="@dimen/_20sdp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvPassword"
        />


    <TextView
        android:id="@+id/tvPasswordConfirm"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_15sdp"
        android:text="Confirm New Password"
        android:textSize="14sp"
        android:fontFamily="@font/inter"
        android:textFontWeight="500"
        android:textColor="#0A0D14"
        android:layout_marginHorizontal="@dimen/_20sdp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/edTxtPassword" />

    <EditText
        android:id="@+id/edTxtPasswordConfirm"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        app:layout_constraintEnd_toEndOf="parent"
        android:inputType="textPassword"
        android:layout_marginTop="@dimen/_2sdp"
        android:textSize="14sp"
        android:fontFamily="@font/inter"
        android:textFontWeight="400"
        android:maxLines="1"
        android:paddingHorizontal="12dp"
        android:background="@drawable/rounded_edittext"
        android:layout_marginHorizontal="@dimen/_20sdp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvPasswordConfirm"
        />


    <com.google.android.material.button.MaterialButton
        android:id="@+id/btnResetPassword"
        android:layout_width="match_parent"
        android:layout_height="54dp"
        android:paddingVertical="0dp"
        android:layout_marginTop="@dimen/_15sdp"
        android:paddingHorizontal="@dimen/_48sdp"
        android:layout_marginHorizontal="@dimen/_20sdp"
        android:textSize="16sp"
        android:fontFamily="@font/inter"
        android:textFontWeight="600"
        android:textColor="@color/white"
        app:cornerRadius="8dp"
        app:backgroundTint="@color/black"
        android:text="Set New Password"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/edTxtPasswordConfirm" />

</androidx.constraintlayout.widget.ConstraintLayout>
    