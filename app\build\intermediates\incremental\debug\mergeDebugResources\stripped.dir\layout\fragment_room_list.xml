<?xml version="1.0" encoding="utf-8"?>
    <android.widget.RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:animateLayoutChanges="true"
        android:orientation="vertical"
    
        >
    
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerview_637093b5"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:paddingBottom="60dp"
                app:layoutManager="LinearLayoutManager"
    
    
    
                />
    
            <ImageView
                android:id="@+id/addRoom"
                android:layout_width="@dimen/_50sdp"
                android:layout_height="@dimen/_50sdp"
                android:layout_marginRight="@dimen/_30sdp"
                android:layout_marginBottom="@dimen/_30sdp"
                android:src="@android:drawable/ic_menu_add"
                android:layout_alignParentRight="true"
                android:layout_alignParentBottom="true"/>
    
    </android.widget.RelativeLayout>
    
      