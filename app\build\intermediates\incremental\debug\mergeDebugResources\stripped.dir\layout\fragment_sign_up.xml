<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/app_bg_color"
    android:id="@+id/coss"
    tools:context="com.manaknight.app.ui.fragments.LoginFragment">


    <include
        android:id="@+id/headerInclude"
        layout="@layout/header"

        />

    <ScrollView
        android:id="@+id/scrollableContent"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:fillViewport="true"
        android:descendantFocusability="beforeDescendants"
        android:focusableInTouchMode="true"
        android:layout_marginTop="@dimen/_1sdp"
        android:layout_marginBottom="@dimen/_20sdp"
        app:layout_constraintTop_toBottomOf="@id/headerInclude"
        app:layout_constraintBottom_toTopOf="@+id/priv"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <LinearLayout
            android:id="@+id/line1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_20sdp"
            android:layout_marginTop="@dimen/_30sdp"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvFirstName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/inter"
                android:text="First Name"
                android:textColor="#0A0D14"
                android:textFontWeight="500"
                android:textSize="14sp"
                tools:ignore="MissingConstraints" />

            <EditText
                android:id="@+id/edTxtFirstName"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:paddingHorizontal="12dp"
                android:inputType="text"
                android:digits="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ "
                android:maxLines="1"
                android:layout_marginTop="@dimen/_2sdp"
                android:textSize="14sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="400"
                android:background="@drawable/rounded_edittext" />
        </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_15sdp"
                android:layout_marginHorizontal="@dimen/_20sdp"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvLastName"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/inter"
                    android:text="Last Name"
                    android:textColor="#0A0D14"
                    android:textFontWeight="500"
                    android:textSize="14sp" />

                <EditText
                    android:id="@+id/edTxtLastName"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:paddingHorizontal="12dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    android:inputType="text"
                    android:digits="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ "
                    android:layout_marginTop="@dimen/_2sdp"
                    android:maxLines="1"
                    android:textSize="14sp"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="400"
                    android:background="@drawable/rounded_edittext"
                    />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_15sdp"
                android:layout_marginHorizontal="@dimen/_20sdp"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvCompanyName"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Company Name"
                    android:textSize="14sp"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="500"
                    android:textColor="#0A0D14" />

                <EditText
                    android:id="@+id/edTxtCompanyName"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:paddingHorizontal="12dp"
                    android:inputType="text"
                    android:digits="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ "
                    android:maxLines="1"
                    android:layout_marginTop="@dimen/_2sdp"
                    android:textSize="14sp"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="400"
                    android:background="@drawable/rounded_edittext"
                    />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_15sdp"
                android:layout_marginHorizontal="@dimen/_20sdp"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvEmail"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Email"
                    android:textSize="14sp"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="500"
                    android:textColor="#0A0D14" />

                <EditText
                    android:id="@+id/edTxtUserName"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:paddingHorizontal="12dp"
                    android:inputType="textEmailAddress"
                    android:maxLines="1"
                    android:layout_marginTop="@dimen/_2sdp"
                    android:textSize="14sp"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="400"
                    android:background="@drawable/rounded_edittext"
                    />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_15sdp"
                android:layout_marginHorizontal="@dimen/_20sdp"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvPassword"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Password"
                    android:textSize="14sp"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="500"
                    android:textColor="#0A0D14" />

                <EditText
                    android:id="@+id/edTxtPassword"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:paddingHorizontal="12dp"
                    android:inputType="textPassword"
                    android:maxLines="1"
                    android:layout_marginTop="@dimen/_2sdp"
                    android:textSize="14sp"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="400"
                    android:background="@drawable/rounded_edittext"

                    />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_20sdp"
                android:layout_marginHorizontal="@dimen/_20sdp"
                android:orientation="vertical">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnSignUp"
                    android:layout_width="match_parent"
                    android:layout_height="54dp"
                    android:paddingVertical="0dp"
                    android:paddingHorizontal="@dimen/_48sdp"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="600"
                    app:cornerRadius="8dp"
                    app:backgroundTint="@color/black"
                    android:text="Continue" />


            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_10sdp"
                android:layout_gravity="center"
                android:layout_marginHorizontal="@dimen/_20sdp"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/btnSignin"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/already_have_account"
                    android:textColor="#525866"
                    android:textSize="14sp"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="400"
                    android:visibility="visible"/>


            </LinearLayout>

        </LinearLayout>





    </ScrollView>

    <LinearLayout
        android:id="@+id/priv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="20sp"
        android:orientation="horizontal"
        android:layout_marginTop="@dimen/_30sdp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <TextView
            android:id="@+id/btnTerms"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Terms and Conditions"
            android:textColor="#868C98"
            android:textFontWeight="500"
            android:textSize="12sp"
            android:fontFamily="@font/inter"/>

        <TextView
            android:id="@+id/btnPolicy"
            android:layout_marginLeft="20sp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Privacy Policy"
            android:textColor="#868C98"
            android:textFontWeight="500"
            android:textSize="12sp"
            android:fontFamily="@font/inter"/>

    </LinearLayout>




</androidx.constraintlayout.widget.ConstraintLayout>
