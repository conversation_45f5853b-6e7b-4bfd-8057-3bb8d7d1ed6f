<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">



    <ImageView
        android:id="@+id/imageViewSplash"
        android:layout_width="212dp"
        android:layout_height="213dp"
        android:layout_marginStart="169dp"
        android:layout_marginTop="120dp"
        android:layout_marginEnd="170dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.435"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_loc_active" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/buttonSplashLogin"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingVertical="0dp"
        android:layout_marginHorizontal="16dp"
        android:layout_marginTop="15dp"
        android:padding="16dp"
        android:text="Login"
        app:cornerRadius="10dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/imageViewSplash"
        app:strokeWidth="2dp" />


    <com.google.android.material.button.MaterialButton
        android:id="@+id/btnSplashRegister"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:paddingVertical="0dp"
        android:layout_marginTop="15dp"
        android:padding="16dp"
        android:text="Register"
        android:textAllCaps="false"
        style="@style/Widget.MaterialComponents.Button.OutlinedButton"
        app:cornerRadius="10dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/buttonSplashLogin"
        app:strokeWidth="2dp" />

      
       

</androidx.constraintlayout.widget.ConstraintLayout>
    