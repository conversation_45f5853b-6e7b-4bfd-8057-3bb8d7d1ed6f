<?xml version="1.0" encoding="utf-8"?>
   <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
    android:background="@color/app_bg_color"
        tools:context="com.manaknight.app.ui.fragments.LoginFragment">
    

    <include
        android:id="@+id/headerInclude"
        layout="@layout/header"

        ></include>


    <androidx.cardview.widget.CardView
        android:id="@+id/dialogCardView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_20sdp"
        app:cardBackgroundColor="@color/white"
        app:cardCornerRadius="@dimen/_16sdp"
        app:cardElevation="1dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/headerInclude">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"

            android:paddingBottom="@dimen/_10sdp">


            <TextView
                android:layout_width="wrap_content"
                android:layout_height="@dimen/_20sdp"
                android:layout_marginTop="@dimen/_15sdp"
                android:text="FREE TRIAL"
                android:layout_marginHorizontal="@dimen/_15sdp"
                android:gravity="center"
                android:paddingVertical="@dimen/_1sdp"
                android:paddingHorizontal="@dimen/_10sdp"
                android:textSize="12sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="500"
                android:background="@drawable/rounded_textview_bg"
                android:textColor="@color/black" />


            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingHorizontal="@dimen/_15sdp"
                android:layout_marginTop="@dimen/_5sdp">

                <TextView
                    android:id="@+id/tvPrice"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="$45"
                    android:layout_centerVertical="true"
                    android:textSize="20sp"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="700"
                    android:textColor="#0A0D14" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="/month"
                    android:layout_centerVertical="true"
                    android:layout_toRightOf="@+id/tvPrice"
                    android:textSize="18sp"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="400"
                    android:textColor="#0A0D14" />


            </RelativeLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/_1sdp"
                android:layout_marginHorizontal="@dimen/_15sdp"
                android:layout_marginTop="@dimen/_10sdp"

                android:layout_marginBottom="@dimen/_10sdp"
                android:background="#E2E4E9"
                />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_5sdp"
                android:paddingHorizontal="@dimen/_15sdp"
                android:text="Start a 14-day free trial for full access to Profit Pro! Manage your business, Know your numbers without the hastle of spreadsheets or miscalculations."
                android:textSize="14sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="400"
                android:textColor="#525866" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingHorizontal="@dimen/_15sdp"
                android:layout_marginTop="@dimen/_20sdp">


                <ImageView
                    android:layout_width="@dimen/_15sdp"
                    android:layout_height="@dimen/_15sdp"
                    android:layout_gravity="center"
                    android:src="@drawable/check" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Precision estimating"
                    android:layout_gravity="center"
                    android:layout_marginLeft="@dimen/_10sdp"
                    android:textSize="14sp"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="400"
                    android:textColor="#000000" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingHorizontal="@dimen/_15sdp"
                android:layout_marginTop="@dimen/_8sdp">


                <ImageView
                    android:layout_width="@dimen/_15sdp"
                    android:layout_height="@dimen/_15sdp"
                    android:layout_gravity="center"
                    android:src="@drawable/check" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Job costing"
                    android:textSize="14sp"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="400"
                    android:layout_gravity="center"
                    android:layout_marginLeft="@dimen/_10sdp"
                    android:textColor="#000000" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingHorizontal="@dimen/_15sdp"
                android:layout_marginTop="@dimen/_8sdp">


                <ImageView
                    android:layout_width="@dimen/_15sdp"
                    android:layout_height="@dimen/_15sdp"
                    android:layout_gravity="center"
                    android:src="@drawable/check" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Payment tracking"
                    android:textSize="14sp"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="400"
                    android:layout_gravity="center"
                    android:layout_marginLeft="@dimen/_10sdp"
                    android:textColor="#000000" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingHorizontal="@dimen/_15sdp"
                android:layout_marginTop="@dimen/_8sdp">


                <ImageView
                    android:layout_width="@dimen/_15sdp"
                    android:layout_height="@dimen/_15sdp"
                    android:layout_gravity="center"
                    android:src="@drawable/check" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Autogenerated pdf documents"
                    android:textSize="14sp"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="400"
                    android:layout_gravity="center"
                    android:layout_marginLeft="@dimen/_10sdp"
                    android:textColor="#000000" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingHorizontal="@dimen/_15sdp"
                android:layout_marginBottom="@dimen/_10sdp"
                android:layout_marginTop="@dimen/_8sdp">


                <ImageView
                    android:layout_width="@dimen/_15sdp"
                    android:layout_height="@dimen/_15sdp"
                    android:layout_gravity="center"
                    android:src="@drawable/check" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Accurate financial reports"
                    android:textSize="14sp"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="400"
                    android:layout_gravity="center"
                    android:layout_marginLeft="@dimen/_10sdp"
                    android:textColor="#000000" />

            </LinearLayout>



        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btnTrail"
        android:layout_width="match_parent"
        android:layout_height="54dp"
        android:paddingVertical="0dp"
        android:layout_marginTop="@dimen/_20sdp"
        android:paddingHorizontal="@dimen/_2sdp"
        android:layout_marginHorizontal="@dimen/_20sdp"
        android:textSize="16sp"
        android:fontFamily="@font/inter"
        android:textFontWeight="600"
        android:textColor="@color/white"
        app:cornerRadius="8dp"
        app:backgroundTint="@color/black"
        android:text="Start 14-Day Free Trail"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/dialogCardView" />

</androidx.constraintlayout.widget.ConstraintLayout>
    