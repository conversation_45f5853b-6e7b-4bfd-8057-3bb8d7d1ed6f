<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:animateLayoutChanges="true"
        
        android:orientation="vertical"
        >

        
        
    <LinearLayout
      android:id="@+id/lytHead"
      android:layout_width="match_parent"
      android:layout_height="@dimen/_39sdp"
      android:orientation="horizontal"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="parent"
      app:layout_constraintEnd_toEndOf="parent"
      android:layout_marginTop="0dp"
      android:background="@color/black"
      android:weightSum="3"
    >
    
  
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:orientation="horizontal"
        android:layout_weight="1">

      <TextView
          android:id="@+id/appCompatTextView2"
          android:layout_width="match_parent"
          android:layout_height="match_parent"
          android:text="Trackingview"
          android:gravity="center"
          android:textAlignment="center"
          android:textColor="@color/white" />
    </LinearLayout>

    
    
  </LinearLayout>
          
        
        <LinearLayout
  android:id="@+id/linearlayout_142b3d45"
  android:layout_width="match_parent"
  android:layout_height="match_parent"
  android:orientation="vertical"
   
  >

  <!-- Child views go here -->
   

</LinearLayout>

</LinearLayout>
