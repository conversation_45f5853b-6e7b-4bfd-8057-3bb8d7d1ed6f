<?xml version="1.0" encoding="utf-8"?>
    
    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingVertical="@dimen/_10sdp"
        android:paddingHorizontal="@dimen/_20sdp"
        tools:ignore="MissingDefaultResource">
    
        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/imageView4"
            android:layout_width="@dimen/_30sdp"
            android:layout_height="@dimen/_30sdp"
            android:scaleType="centerCrop"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.5"
            app:shapeAppearanceOverlay="@style/ShapeAppearance.Material3.Corner.Full"
            app:srcCompat="@android:drawable/ic_menu_gallery" />
    
        <TextView
            android:id="@+id/textViewAlertMsg"
            style="@style/text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_10sdp"
            android:ellipsize="end"
            android:singleLine="true"
            android:maxLines="1"
            app:layout_constraintBottom_toTopOf="@+id/tvTimeAgo"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/imageView4"
            app:layout_constraintTop_toTopOf="@+id/imageView4"
            tools:text="" />
    
        <TextView
            android:id="@+id/tvTimeAgo"
            style="@style/small"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:singleLine="true"
            android:maxLines="1"
            android:textColor="@color/gray"
            app:layout_constraintBottom_toBottomOf="@+id/imageView4"
            app:layout_constraintStart_toStartOf="@+id/textViewAlertMsg"
            app:layout_constraintTop_toBottomOf="@+id/textViewAlertMsg"
            tools:text="" />
    
    
    
    </androidx.constraintlayout.widget.ConstraintLayout>
    
    