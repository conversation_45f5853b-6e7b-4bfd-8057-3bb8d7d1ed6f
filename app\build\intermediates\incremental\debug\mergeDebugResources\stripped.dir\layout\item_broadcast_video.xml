<?xml version="1.0" encoding="utf-8"?>
    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_marginHorizontal="@dimen/_30sdp"
        android:layout_marginTop="@dimen/_8sdp"
        android:layout_height="wrap_content">
    
        <androidx.cardview.widget.CardView
            android:id="@+id/cvImage"
            android:layout_width="@dimen/_80sdp"
            android:layout_height="@dimen/_80sdp"
            app:cardCornerRadius="@dimen/_10sdp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">
    
            <ImageView
                android:id="@+id/imageView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:src="@color/gray"
                android:scaleType="centerCrop"
                tools:ignore="ContentDescription" />
    
        </androidx.cardview.widget.CardView>
    
        <TextView
            android:id="@+id/tvName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_10sdp"
            android:layout_marginTop="@dimen/_5sdp"
            android:textColor="@color/black"
            android:textSize="@dimen/_14sdp"
            app:layout_constraintStart_toEndOf="@+id/cvImage"
            app:layout_constraintTop_toTopOf="parent" />
    
        <TextView
            android:id="@+id/tvCountry"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_10sdp"
            android:layout_marginTop="@dimen/_5sdp"
            android:textColor="@color/black"
            android:textSize="@dimen/_12sdp"
            app:layout_constraintStart_toEndOf="@+id/cvImage"
            app:layout_constraintTop_toBottomOf="@+id/tvName" />
    
        <TextView
            android:id="@+id/tvUploadTime"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_2sdp"
            android:layout_marginEnd="@dimen/_8sdp"
            android:textColor="@color/black"
            android:textSize="@dimen/_12sdp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvCountry" />
    
        <TextView
            android:id="@+id/tvPrice"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/black"
            android:textSize="@dimen/_12sdp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="@+id/tvCountry" />
    
    </androidx.constraintlayout.widget.ConstraintLayout>

      