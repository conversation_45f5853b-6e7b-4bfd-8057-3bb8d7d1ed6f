<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/bg_stat_card"
    android:padding="16dp"
    android:layout_marginBottom="12dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/txtProjectId"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="#123"
                android:textSize="16sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="500"
                android:textColor="@color/text_sub" />

            <TextView
                android:id="@+id/txtProjectName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="(project.name)"
                android:textSize="14sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="500"
                android:textColor="@color/profit_black"
                android:layout_marginTop="4dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginTop="8dp">
                <ImageView
                    android:layout_width="@dimen/_20sdp"
                    android:layout_height="@dimen/_13sdp"
                    android:src="@drawable/profit"/>

<!--                <TextView-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:text="💰"-->
<!--                    android:textSize="14sp"-->
<!--                    android:layout_marginEnd="4dp" />-->

                <TextView
                    android:id="@+id/txtProjectProfit"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Profit: $1275.00 | Status: Active"
                    android:textSize="12sp"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="500"
                    android:textColor="@color/profit_black"
                    android:maxLines="2"
                    android:ellipsize="end" />

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="end">
            <ImageView
                android:id="@+id/imgProjectMenu"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_option_light"
                android:layout_marginTop="8dp"
                android:contentDescription="More options" />

            <TextView
                android:id="@+id/txtProjectDate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="mm/dd/yy"
                android:textSize="12sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="400"
                android:textColor="@color/profit_grey"
                android:layout_marginTop="8dp" />

               </LinearLayout>

    </LinearLayout>

</LinearLayout>
