<?xml version="1.0" encoding="utf-8"?>
    
    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingVertical="@dimen/_10sdp"
        android:paddingHorizontal="0dp"
        tools:ignore="MissingDefaultResource">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingHorizontal="@dimen/_10sdp"
        android:paddingVertical="@dimen/_10sdp"
                android:background="@drawable/rounded_edittext"
        app:layout_constraintStart_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">


        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Emplyee #1 (Owner)"
                android:textSize="14sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="500"
                android:layout_centerVertical="true"
                android:textColor="#000000"/>

            <ImageView
                android:id="@+id/btnEdit"
                android:layout_width="@dimen/_32sdp"
                android:layout_height="@dimen/_32sdp"
                android:layout_centerVertical="true"
                android:layout_alignParentEnd="true"
                android:layout_marginRight="@dimen/_5sdp"
                android:src="@drawable/edit" />

            <ImageView
                android:id="@+id/btnDelete"
                android:layout_width="@dimen/_32sdp"
                android:layout_height="@dimen/_32sdp"
                android:layout_centerVertical="true"
                android:layout_toStartOf="@+id/btnEdit"
                android:layout_marginRight="@dimen/_1sdp"
                android:src="@drawable/delete" />



        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_15sdp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Full Name"
                android:textSize="14sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="400"
                android:layout_centerVertical="true"
                android:textColor="#525866"/>

            <TextView
                android:id="@+id/txtFName"
                android:textAlignment="textEnd"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Kapil Kumar"
                android:textSize="14sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="500"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:textColor="#000000"/>

        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_10sdp"
            android:layout_marginBottom="@dimen/_10sdp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Hourly Rate"
                android:textSize="14sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="400"
                android:layout_centerVertical="true"
                android:textColor="#525866"/>

            <TextView
                android:id="@+id/txtRate"
                android:textAlignment="textEnd"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="$30.00"
                android:textSize="14sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="500"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:textColor="#000000"/>

        </RelativeLayout>



    </LinearLayout>




    </androidx.constraintlayout.widget.ConstraintLayout>
    
    