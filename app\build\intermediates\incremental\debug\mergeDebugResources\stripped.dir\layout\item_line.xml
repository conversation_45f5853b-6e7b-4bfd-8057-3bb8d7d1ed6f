<?xml version="1.0" encoding="utf-8"?>
    
    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingVertical="@dimen/_10sdp"
        android:paddingHorizontal="@dimen/_16sdp"
        tools:ignore="MissingDefaultResource">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingHorizontal="@dimen/_10sdp"
        android:paddingVertical="@dimen/_10sdp"
                android:background="@drawable/rounded_edittext"
        app:layout_constraintStart_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">


        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/description"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Emplyee #1 (Owner)"
                android:textSize="14sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="500"
                android:layout_centerVertical="true"
                android:textColor="#0A0D14"/>

            <ImageView
                android:id="@+id/btnDelete"
                android:layout_width="@dimen/_32sdp"
                android:layout_height="@dimen/_32sdp"
                android:layout_centerVertical="true"
                android:layout_alignParentEnd="true"
                android:layout_marginRight="@dimen/_5sdp"
                android:src="@drawable/delete" />

            <ImageView
                android:id="@+id/btnEdit"
                android:layout_width="@dimen/_32sdp"
                android:layout_height="@dimen/_32sdp"
                android:layout_centerVertical="true"
                android:layout_toStartOf="@+id/btnDelete"
                android:layout_marginRight="@dimen/_1sdp"
                android:src="@drawable/edit" />



        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_15sdp">

            <TextView
                android:id="@+id/txtSalePrice"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Sale Price"
                android:textSize="14sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="400"
                android:layout_centerVertical="true"
                android:textColor="#525866"/>

            <TextView
                android:id="@+id/txtSalePrice_1"
                android:textAlignment="textEnd"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="@dimen/_10sdp"
                android:text="Kapil Kumar"
                android:textSize="14sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="500"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:textColor="#000000"/>

        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_10sdp">

            <TextView
                android:id="@+id/txtLaboutBudget"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Labor Budget"
                android:textSize="14sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="400"
                android:layout_centerVertical="true"
                android:textColor="#525866"/>

            <TextView
                android:id="@+id/txtLaboutBudget_1"
                android:textAlignment="textEnd"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="@dimen/_10sdp"
                android:text="Kapil Kumar"
                android:textSize="14sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="500"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:textColor="#000000"/>

        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_10sdp">

            <TextView
                android:id="@+id/txtProfitOverhead"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Profit Overhead"
                android:textSize="14sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="400"
                android:layout_centerVertical="true"
                android:textColor="#525866"/>

            <TextView
                android:id="@+id/txtProfitOverhead_1"
                android:textAlignment="textEnd"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="@dimen/_10sdp"
                android:text="Kapil Kumar"
                android:textSize="14sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="500"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:textColor="#000000"/>

        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_10sdp">

            <TextView
                android:id="@+id/txtMaterialBudget"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Material budget"
                android:textSize="14sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="400"
                android:layout_centerVertical="true"
                android:textColor="#525866"/>

            <TextView
                android:id="@+id/txtMaterialBudget_1"
                android:textAlignment="textEnd"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="@dimen/_10sdp"
                android:text="Kapil Kumar"
                android:textSize="14sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="500"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:textColor="#000000"/>

        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/_1sdp"
            android:layout_marginTop="@dimen/_15sdp"
            android:background="#E2E4E9"/>

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_10sdp"
            android:layout_marginBottom="@dimen/_8sdp"
            android:paddingHorizontal="@dimen/_5sdp"
            android:paddingVertical="@dimen/_2sdp"
            android:background="@drawable/bg_round_corners_2">

            <TextView
                android:id="@+id/txtTypeLabel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Material budget"
                android:textSize="12sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="500"
                android:layout_centerVertical="true"
                android:textColor="#101828"/>


        </RelativeLayout>




    </LinearLayout>



    
    </androidx.constraintlayout.widget.ConstraintLayout>
    
    