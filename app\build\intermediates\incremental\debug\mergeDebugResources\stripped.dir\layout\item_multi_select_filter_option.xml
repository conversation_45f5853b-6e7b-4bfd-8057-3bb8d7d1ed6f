<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:background="?android:attr/selectableItemBackground"
    android:padding="16dp"
    android:gravity="center_vertical">

    <CheckBox
        android:id="@+id/cbFilterOption"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:button="@drawable/custom_checkbox_selector"
        android:buttonTint="@null"
        android:backgroundTint="@null"
        android:layout_marginEnd="12dp" />

    <TextView
        android:id="@+id/tvFilterOption"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="Option"
        android:textSize="16sp"
        android:fontFamily="@font/inter"
        android:textColor="@color/profit_black"
        android:gravity="start|center_vertical" />

</LinearLayout>
