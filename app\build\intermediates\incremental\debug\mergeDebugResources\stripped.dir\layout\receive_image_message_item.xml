<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="5dp">

    <ImageView
        android:id="@+id/imgProfile"
        android:layout_width="45sp"
        android:layout_height="45sp"
        android:background="@drawable/image_shape"
        android:contentDescription="@string/image"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/txtUsername"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_margin="3dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:text="@string/username"
        android:textColor="@android:color/black"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/imgProfile"
        app:layout_constraintTop_toTopOf="@id/imgProfile" />

    <androidx.cardview.widget.CardView
        android:id="@+id/txtMessage"
        android:layout_width="200sp"
        android:layout_height="200sp"
        android:layout_margin="3dp"
        app:cardBackgroundColor="#B7DAF6"
        app:cardCornerRadius="25sp"
        app:layout_constraintStart_toEndOf="@id/imgProfile"
        app:layout_constraintTop_toBottomOf="@id/txtUsername">

        <ImageView
            android:id="@+id/imgMessage"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:contentDescription="@string/image"
            android:scaleType="centerCrop" />

    </androidx.cardview.widget.CardView>

    <TextView
        android:id="@+id/txtDate"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="3dp"
        android:text="@string/date"
        android:textColor="@android:color/darker_gray"
        android:textSize="12sp"
        app:layout_constraintStart_toEndOf="@id/imgProfile"
        app:layout_constraintTop_toBottomOf="@id/txtMessage" />

</androidx.constraintlayout.widget.ConstraintLayout>
      