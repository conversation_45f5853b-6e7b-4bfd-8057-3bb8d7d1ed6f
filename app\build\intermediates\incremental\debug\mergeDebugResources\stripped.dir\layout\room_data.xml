<?xml version="1.0" encoding="utf-8"?>
    <androidx.cardview.widget.CardView
      xmlns:android="http://schemas.android.com/apk/res/android"
      xmlns:app="http://schemas.android.com/apk/res-auto"
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      app:cardCornerRadius="8dp"
      app:cardElevation="4dp"
      app:cardUseCompatPadding="true">
  
      <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
          xmlns:tools="http://schemas.android.com/tools"
          android:layout_width="match_parent"
          android:layout_height="wrap_content"
          android:orientation="vertical"
          android:padding="16dp">
            
      <TextView
          android:id="@+id/data_id"
          android:layout_width="wrap_content"
          android:layout_height="wrap_content"
          android:text="id"
          android:textSize="12sp"
          android:padding="2dp"
          android:layout_margin="2dp"
          android:textColor="@android:color/black" />
      
  
      </LinearLayout>
  
      </androidx.cardview.widget.CardView>
      