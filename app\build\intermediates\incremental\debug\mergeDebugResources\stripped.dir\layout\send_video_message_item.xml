<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="5dp">


    <androidx.cardview.widget.CardView
        android:id="@+id/txtMessage"
        android:layout_width="200sp"
        android:layout_height="200sp"
        android:padding="1dp"
        app:cardBackgroundColor="#BFC1C0"
        app:cardCornerRadius="25sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:padding="1dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/imgMessage"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:contentDescription="@string/image"
                android:scaleType="centerCrop" />

            <ImageView
                android:layout_width="45sp"
                android:layout_height="45sp"
                android:contentDescription="@string/image"
                android:src="@drawable/ic_baseline_play_circle_24"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:tint="@android:color/darker_gray" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.cardview.widget.CardView>

    <TextView
        android:id="@+id/txtDate"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="3dp"
        android:text="@string/date"
        android:textColor="#000"
        android:textSize="12sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/txtMessage" />


</androidx.constraintlayout.widget.ConstraintLayout>
      