<?xml version="1.0" encoding="UTF-8"?>
    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:id="@+id/simpleChatView"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
    
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvChats"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toTopOf="@id/layoutChatInputHolder"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    
    
        <androidx.appcompat.widget.LinearLayoutCompat
            android:id="@+id/moreLayout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="8sp"
            android:orientation="vertical"
            android:visibility="invisible"
            app:layout_constraintBottom_toTopOf="@id/layoutChatInputHolder"
            app:layout_constraintStart_toStartOf="parent">
    
            <ImageView
                android:id="@+id/imgImage"
                android:layout_width="50sp"
                android:layout_height="50sp"
                android:background="@drawable/select_image_shape"
                android:contentDescription="@string/send_image"
                android:padding="8sp"
                android:src="@drawable/ic_baseline_image_24" />
    
            <ImageView
                android:id="@+id/imgVideo"
                android:layout_width="50sp"
                android:layout_height="50sp"
                android:layout_marginTop="5dp"
                android:background="@drawable/select_video_shape"
                android:contentDescription="@string/send_video"
                android:padding="8sp"
                android:src="@drawable/ic_baseline_video_library_24" />
    
            <ImageView
                android:id="@+id/imgCamera"
                android:layout_width="50sp"
                android:layout_height="50sp"
                android:layout_marginTop="5dp"
                android:background="@drawable/select_camera_shape"
                android:contentDescription="@string/open_camera"
                android:padding="8sp"
                android:src="@drawable/ic_baseline_camera_alt_24" />
    
        </androidx.appcompat.widget.LinearLayoutCompat>
    
        <LinearLayout
            android:id="@+id/layoutChatInputHolder"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_margin="8sp"
            android:background="@drawable/chat_input_shape"
            android:orientation="horizontal"
            android:padding="5dp"
            android:gravity="center"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/btnSend"
            app:layout_constraintStart_toStartOf="parent">
    
            <ImageView
                android:id="@+id/btnAdd"
                android:layout_width="35sp"
                android:layout_height="35sp"
                android:layout_gravity="bottom"
                android:layout_marginBottom="3dp"
                android:background="@drawable/add_shape"
                android:contentDescription="@string/add_button"
                android:padding="8sp"
                android:src="@drawable/ic_baseline_add_24" />
    
            <EditText
                android:id="@+id/edtMessage"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:autofillHints="@null"
                android:background="@drawable/chat_input_shape"
                android:hint="@string/type_message"
                android:inputType="textMultiLine"
                android:maxLines="5"
                android:padding="8sp" />
    
        </LinearLayout>
    
        <ImageView
            android:id="@+id/btnSend"
            android:layout_width="45sp"
            android:layout_height="45sp"
            android:layout_margin="8sp"
            android:background="@drawable/send_button_shape"
            android:contentDescription="@string/send_button"
            android:padding="8sp"
            android:layout_marginBottom="3dp"
            android:src="@drawable/ic_baseline_send_24"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />
    
    
    </androidx.constraintlayout.widget.ConstraintLayout>
     
      