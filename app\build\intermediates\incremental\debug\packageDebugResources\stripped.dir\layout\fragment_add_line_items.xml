<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/app_bg_color"
    android:id="@+id/coss"
    tools:context="com.manaknight.app.ui.fragments.LoginFragment">


    <include
        android:id="@+id/headerInclude"
        layout="@layout/header"

        />

    <LinearLayout
        android:id="@+id/dataLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginHorizontal="@dimen/_15sdp"
        app:layout_constraintTop_toBottomOf="@id/headerInclude"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:visibility="visible"
        >

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingHorizontal="@dimen/_12sdp"
            android:paddingVertical="@dimen/_10sdp"
            android:layout_marginTop="16sp"
            android:background="@drawable/rounded_edittext"
            app:layout_constraintStart_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginTop="@dimen/_10sdp">

                <TextView
                    android:id="@+id/lineItem"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Line Item #1"
                    android:textSize="14sp"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="500"
                    android:layout_centerVertical="true"
                    android:textColor="@color/profit_black"/>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_10sdp"
                    android:text="Description"
                    android:textSize="14sp"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="500"
                    android:layout_centerVertical="true"
                    android:textColor="@color/profit_black"/>

                <EditText
                    android:id="@+id/descInput"
                    android:layout_width="match_parent"
                    android:layout_height="130dp"
                    android:background="@drawable/rounded_edittext"
                    android:padding="10dp"
                    android:textSize="14sp"

                    android:fontFamily="@font/inter"
                    android:textFontWeight="500"
                    android:gravity="top"
                    android:hint="Enter description"

                    android:layout_marginTop="8dp"/>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_10sdp"
                    android:text="Estimate by"
                    android:textSize="14sp"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="500"

                    android:layout_centerVertical="true"
                    android:textColor="@color/profit_black"/>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginTop="8dp"
                    >

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnMaterial"
                        android:layout_width="0dp"
                        android:layout_height="44dp"
                        android:paddingVertical="0dp"
                        android:layout_weight="1"
                        android:layout_marginEnd="5dp"
                        android:textColor="@color/black"
                        app:cornerRadius="8dp"
                        app:strokeColor="@color/black"
                        app:strokeWidth="1dp"
                        app:backgroundTint="@color/white"
                        android:textSize="13sp"
                        android:fontFamily="@font/inter"
                        android:textFontWeight="400"
                        android:text="Material" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnLinearFt"
                        android:layout_width="0dp"
                        android:layout_height="44dp"
                        android:paddingVertical="0dp"
                        android:layout_weight="1"
                        android:layout_marginEnd="5dp"
                        android:textColor="@color/black"
                        app:cornerRadius="8dp"
                        app:strokeColor="@color/black"
                        app:strokeWidth="1dp"
                        app:backgroundTint="@color/white"
                        android:textSize="13sp"
                        android:fontFamily="@font/inter"
                        android:textFontWeight="400"
                        android:text="Linear ft" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnSquareFt"
                        android:layout_width="0dp"
                        android:layout_height="44dp"
                        android:paddingVertical="0dp"
                        android:layout_weight="1"
                        android:fontFamily="@font/inter"
                        android:text="Square ft"
                        android:textColor="@color/black"
                        android:textFontWeight="400"
                        android:textSize="13sp"
                        app:backgroundTint="@color/white"
                        app:cornerRadius="8dp"
                        app:strokeColor="@color/black"
                        app:strokeWidth="1dp" />

                </LinearLayout>


            </LinearLayout>



        </LinearLayout>


    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>
