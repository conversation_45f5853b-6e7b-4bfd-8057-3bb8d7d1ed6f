<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/app_bg_color"
    android:id="@+id/coss"
    tools:context="com.manaknight.app.ui.fragments.LoginFragment">


    <include
        android:id="@+id/headerInclude"
        layout="@layout/header"

        />

    <ScrollView
        android:id="@+id/scrollableContent"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:fillViewport="true"
        android:descendantFocusability="beforeDescendants"
        android:focusableInTouchMode="true"
        android:layout_marginTop="@dimen/_1sdp"
        android:layout_marginBottom="@dimen/_20sdp"
        app:layout_constraintTop_toBottomOf="@id/headerInclude"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <LinearLayout
            android:id="@+id/line1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"

            android:orientation="vertical">


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_margin="@dimen/_16sdp"
            android:paddingBottom="@dimen/_20sdp"

            android:background="@drawable/rounded_edittext"
            >
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_16sdp"
            android:layout_marginTop="@dimen/_20sdp"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvFirstName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/inter"
                android:text="Name"
                android:textColor="#0A0D14"
                android:textFontWeight="500"
                android:textSize="14sp"
                tools:ignore="MissingConstraints" />

            <EditText
                android:id="@+id/edTxtName"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:paddingHorizontal="12dp"
                android:inputType="text"
                android:text=""
                android:digits="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ "
                android:maxLines="1"
                android:layout_marginTop="@dimen/_2sdp"
                android:textSize="14sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="400"
                android:background="@drawable/rounded_edittext" />
        </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_15sdp"
                android:layout_marginHorizontal="@dimen/_16sdp"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvLastName"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/inter"
                    android:text="Address"
                    android:textColor="#0A0D14"
                    android:textFontWeight="500"
                    android:textSize="14sp" />

                <EditText
                    android:id="@+id/edTxtAddress"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:paddingHorizontal="12dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    android:inputType="text"
                    android:digits="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890 #-_*,\\"
                    android:layout_marginTop="@dimen/_2sdp"
                    android:maxLines="1"
                    android:textSize="14sp"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="400"
                    android:background="@drawable/rounded_edittext"
                    />

            </LinearLayout>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_15sdp"
                android:layout_marginHorizontal="@dimen/_16sdp"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvEmail"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Email"
                    android:textSize="14sp"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="500"
                    android:textColor="#0A0D14" />

                <EditText
                    android:id="@+id/edTxtUserName"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:paddingHorizontal="12dp"
                    android:inputType="textEmailAddress"
                    android:maxLines="1"
                    android:layout_marginTop="@dimen/_2sdp"
                    android:textSize="14sp"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="400"
                    android:background="@drawable/rounded_edittext"
                    />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_15sdp"
                android:layout_marginHorizontal="@dimen/_16sdp"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvPhone"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Phone Number"
                    android:textSize="14sp"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="500"
                    android:textColor="#0A0D14" />

                <EditText
                    android:id="@+id/edTxtPhone"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:paddingHorizontal="12dp"
                    android:inputType="phone"
                    android:maxLines="1"
                    android:layout_marginTop="@dimen/_2sdp"
                    android:textSize="14sp"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="400"
                    android:background="@drawable/rounded_edittext"
                    />

            </LinearLayout>
        </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_10sdp"
                android:layout_marginHorizontal="@dimen/_16sdp"
                android:orientation="vertical">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnContinue"
                    android:layout_width="match_parent"
                    android:layout_height="54dp"
                    android:fontFamily="@font/inter"
                    android:paddingHorizontal="@dimen/_48sdp"
                    android:paddingVertical="0dp"
                    android:text="Continue"
                    android:textColor="@color/white"
                    android:textFontWeight="600"
                    android:textSize="16sp"
                    app:backgroundTint="@color/black"
                    app:cornerRadius="8dp" />


            </LinearLayout>



        </LinearLayout>





    </ScrollView>


</androidx.constraintlayout.widget.ConstraintLayout>
