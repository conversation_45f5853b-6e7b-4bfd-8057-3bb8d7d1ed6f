<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/constraint"
    android:background="@color/app_bg_color">


    <include
        android:id="@+id/headerInclude"
        layout="@layout/header"

        ></include>

    <ScrollView
        android:id="@+id/scrollable"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:focusableInTouchMode="true"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginTop="@dimen/_60sdp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/headerInclude"
        >

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:id="@+id/line1"
            android:orientation="vertical">


            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Customer"
                android:textSize="14sp"
                android:layout_marginHorizontal="@dimen/_15sdp"
                android:fontFamily="@font/inter"
                android:textFontWeight="500"
                android:layout_centerVertical="true"
                android:textColor="@color/profit_black"/>

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnSearch"
                android:layout_width="match_parent"
                android:layout_height="54dp"
                android:paddingVertical="0dp"
                android:layout_marginTop="@dimen/_2sdp"
                android:layout_marginHorizontal="@dimen/_15sdp"
                android:textColor="#868C98"
                app:cornerRadius="8dp"
                app:strokeColor="#E2E4E9"
                app:strokeWidth="1dp"
                app:backgroundTint="@color/white"
                android:textSize="16sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="500"
                android:text="- Select -"
                android:textAlignment="textStart"
                app:icon="@drawable/chevron_bottom"
                app:iconGravity="end"
                app:iconTint="#525866"/>
            <com.google.android.material.button.MaterialButton
                android:id="@+id/createCustomer"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:paddingVertical="0dp"
                android:textColor="#375DFB"
                android:layout_marginHorizontal="@dimen/_15sdp"
                android:textSize="16sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="500"
                app:cornerRadius="8dp"
                app:strokeWidth="2dp"
                app:strokeColor="#375DFB"
                app:backgroundTint="@color/white"
                android:layout_marginTop="@dimen/_4sdp"
                android:layout_below="@+id/noMatch"
                android:text="Create New Customer" />

            <LinearLayout
                android:id="@+id/dataLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginHorizontal="@dimen/_15sdp"
                android:visibility="gone"
                >

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"

                android:paddingHorizontal="@dimen/_12sdp"
                android:paddingVertical="@dimen/_10sdp"
                android:layout_marginTop="16sp"
                android:background="@drawable/rounded_edittext"
                app:layout_constraintStart_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_marginTop="@dimen/_10sdp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Address"
                        android:textSize="16sp"
                        android:fontFamily="@font/inter"
                        android:textFontWeight="400"
                        android:layout_centerVertical="true"
                        android:textColor="#525866"/>

                    <TextView
                        android:id="@+id/txtAddress"
                        android:textAlignment="textEnd"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Kapil Kumar"
                        android:textSize="16sp"
                        android:fontFamily="@font/inter"
                        android:textFontWeight="500"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:textColor="#000000"/>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_marginTop="@dimen/_10sdp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Email"
                        android:textSize="16sp"
                        android:fontFamily="@font/inter"
                        android:textFontWeight="400"
                        android:layout_centerVertical="true"
                        android:textColor="#525866"/>

                    <TextView
                        android:id="@+id/txtEmail"
                        android:textAlignment="textEnd"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="{Email}"
                        android:textSize="16sp"
                        android:fontFamily="@font/inter"
                        android:textFontWeight="500"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:textColor="#000000"/>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_marginTop="@dimen/_10sdp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Phone Number"
                        android:textSize="16sp"
                        android:fontFamily="@font/inter"
                        android:textFontWeight="400"
                        android:layout_centerVertical="true"
                        android:textColor="#525866"/>

                    <TextView
                        android:id="@+id/txtPhoneNumber"
                        android:textAlignment="textEnd"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="{Phone Number}"
                        android:textSize="16sp"
                        android:fontFamily="@font/inter"
                        android:textFontWeight="500"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:textColor="#000000"/>

                </LinearLayout>


            </LinearLayout>


            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnEditCustomer"
                android:layout_width="match_parent"
                android:layout_height="54dp"
                android:paddingVertical="0dp"
                android:layout_marginTop="@dimen/_20sdp"
                android:paddingHorizontal="@dimen/_48sdp"
                android:textColor="@color/black"
                app:cornerRadius="8dp"
                app:strokeColor="@color/black"
                app:strokeWidth="2dp"
                app:backgroundTint="@color/white"
                android:textSize="16sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="600"
                android:text="Edit Customer"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/linearDefaultProfitOverhead3" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnContinue"
                android:layout_width="match_parent"
                android:layout_height="54dp"
                android:paddingVertical="0dp"
                android:layout_marginTop="@dimen/_10sdp"
                android:paddingHorizontal="@dimen/_48sdp"
                android:textColor="@color/white"
                app:cornerRadius="8dp"
                app:backgroundTint="@color/black"
                android:textSize="16sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="600"
                android:text="Continue"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/linearDefaultProfitOverhead3" />
            </LinearLayout>


        </LinearLayout>

    </ScrollView>







</androidx.constraintlayout.widget.ConstraintLayout>
