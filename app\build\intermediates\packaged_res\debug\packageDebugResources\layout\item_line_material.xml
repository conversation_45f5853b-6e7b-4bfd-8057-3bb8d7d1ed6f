<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="@dimen/_2sdp"
    android:layout_marginRight="@dimen/_2sdp"
    app:cardCornerRadius="8dp"
    android:background="@drawable/rounded_edittext"
    android:layout_marginTop="10dp"
    android:layout_marginBottom="10dp"
    android:focusable="true"
    android:focusableInTouchMode="true">



<androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingHorizontal="@dimen/_12sdp">

        <CheckBox
            android:id="@+id/checkBoxMaterial"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:button="@drawable/custom_checkbox_selector"
            android:buttonTint="@color/brand_green"
            android:backgroundTint="@color/brand_green"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>


        <TextView
            android:id="@+id/txtMaterialName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="Material Name"
            android:textSize="16sp"
            android:fontFamily="@font/inter"
            android:textFontWeight="500"
            android:textColor="@color/black"
            android:layout_marginTop="@dimen/_8sdp"
            app:layout_constraintStart_toEndOf="@id/checkBoxMaterial"
            app:layout_constraintBaseline_toBaselineOf="@id/checkBoxMaterial"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

        <TextView
            android:id="@+id/txtPrice"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="$1/unit"
            android:textSize="14sp"
            android:fontFamily="@font/inter"
            android:textFontWeight="400"
            android:textColor="#525866"
            android:layout_marginTop="@dimen/_2sdp"
            app:layout_constraintStart_toStartOf="@id/txtMaterialName"
            app:layout_constraintTop_toBottomOf="@id/txtMaterialName"/>

        <TextView
            android:id="@+id/txtTotalPrice"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Total: $20"
            android:fontFamily="@font/inter"
            android:textFontWeight="400"
            android:textColor="#525866"
            android:layout_marginTop="@dimen/_2sdp"
            android:layout_marginLeft="@dimen/_50sdp"
            app:layout_constraintStart_toEndOf=" @+id/txtPrice"
            app:layout_constraintTop_toBottomOf="@id/txtMaterialName"/>


        <TextView
            android:id="@+id/txtUnitStart"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="*"
            android:textSize="14sp"
            android:fontFamily="@font/inter"
            android:textFontWeight="500"
            android:textColor="#DF1C41"
            android:layout_marginTop="@dimen/_5sdp"
            app:layout_constraintStart_toStartOf="@id/txtPrice"
            app:layout_constraintTop_toBottomOf="@id/txtPrice"/>

        <TextView
            android:id="@+id/txtUnit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Units"
            android:textSize="14sp"
            android:fontFamily="@font/inter"
            android:textFontWeight="500"
            android:textColor="@color/profit_black"
            android:layout_marginTop="@dimen/_5sdp"
            android:layout_marginLeft="@dimen/_1sdp"
            app:layout_constraintStart_toEndOf=" @+id/txtUnitStart"
            app:layout_constraintTop_toBottomOf="@id/txtPrice"/>


        <EditText
            android:id="@+id/edtUnits"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:hint="* Units"
            android:inputType="text"
            android:digits="0123456789 "
            android:maxLines="1"
            android:textSize="14sp"
            android:fontFamily="@font/inter"
            android:textFontWeight="400"
            android:padding="12dp"
            android:layout_marginTop="@dimen/_2sdp"
            android:background="@drawable/rounded_edittext"
            app:layout_constraintStart_toStartOf="@id/txtMaterialName"
            app:layout_constraintTop_toBottomOf="@id/txtUnit"
            app:layout_constraintEnd_toEndOf="parent"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toEndOf=" @+id/txtUnitStart"
            app:layout_constraintTop_toBottomOf="@id/edtUnits"/>


    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView>
