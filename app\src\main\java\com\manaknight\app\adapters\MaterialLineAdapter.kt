
        
    package com.manaknight.app.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import Manaknight.R
import Manaknight.databinding.ItemLineMaterialBinding
import Manaknight.databinding.ItemMaterialBinding
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import com.manaknight.app.extensions.getTimeInAgo
import com.manaknight.app.extensions.hide
import com.manaknight.app.extensions.loadImageFromUrl
import com.manaknight.app.extensions.setOnClickWithDebounce
import com.manaknight.app.extensions.show
import com.manaknight.app.model.remote.EmplyeeModel
import com.manaknight.app.model.remote.profitPro.MaterialRespListModel
import android.view.MotionEvent
import android.widget.EditText
import android.graphics.Rect
import android.view.inputmethod.InputMethodManager
import android.app.Activity


    class MaterialLineAdapter(
        val onCheckBoxClick: (MaterialRespListModel, Int) -> Unit,
) :
    RecyclerView.Adapter<MaterialLineAdapter.ViewHolder>() {

    val list: ArrayList<MaterialRespListModel> = ArrayList()

    inner class ViewHolder(val binding: ItemLineMaterialBinding) :
        RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val itemView =
            ItemLineMaterialBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ViewHolder(itemView)
    }

        override fun onBindViewHolder(holder: ViewHolder, position: Int) {
            val model = list[position]

            with(holder.binding) {
                checkBoxMaterial.setOnCheckedChangeListener(null) // ✅ Remove previous listener
                checkBoxMaterial.isChecked = model.isSelected == true // ✅ Set state from model

                txtMaterialName.setText(model.name)
                txtPrice.text = "$${model.cost ?: "0"}/unit"

                if (model.isSelected == true) {
                    txtTotalPrice.text = "Total: $%.2f".format(calculateTotal(model))
                }

                // Add touch listener to handle focus clearing when clicking outside EditText
                root.setOnTouchListener { _, event ->
                    if (event.action == MotionEvent.ACTION_DOWN) {
                        val currentFocus = edtUnits
                        if (currentFocus.hasFocus()) {
                            val outRect = Rect()
                            currentFocus.getGlobalVisibleRect(outRect)
                            if (!outRect.contains(event.rawX.toInt(), event.rawY.toInt())) {
                                currentFocus.clearFocus()
                                // Hide keyboard
                                val imm = root.context.getSystemService(Activity.INPUT_METHOD_SERVICE) as InputMethodManager
                                imm.hideSoftInputFromWindow(currentFocus.windowToken, 0)
                            }
                        }
                    }
                    false // Allow other touch events to be processed
                }

                // ✅ Show/hide views based on checkbox state
                if (checkBoxMaterial.isChecked) {
                    txtTotalPrice.visibility = View.VISIBLE
                    txtUnitStart.visibility = View.VISIBLE
                    txtUnit.visibility = View.VISIBLE
                    edtUnits.visibility = View.VISIBLE

                    edtUnits.setText(model.units?.toString() ?: "0") // Set initial value
                } else {
                    txtTotalPrice.visibility = View.GONE
                    txtUnitStart.visibility = View.GONE
                    txtUnit.visibility = View.GONE
                    edtUnits.visibility = View.GONE
                }

                edtUnits.setOnFocusChangeListener { _, hasFocus ->
                    if (hasFocus && edtUnits.text.toString() == "0") {
                        edtUnits.setText("")
                    }
                }

                edtUnits.addTextChangedListener(object : TextWatcher {
                    override fun afterTextChanged(s: Editable?) {
                        model.units = s?.toString()?.toIntOrNull() ?: 0
                        txtTotalPrice.text = "Total: $%.2f".format(calculateTotal(model))
                        onCheckBoxClick(model, position)
                    }

                    override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
                    override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
                })

                // ✅ Set new listener after restoring state
                checkBoxMaterial.setOnCheckedChangeListener { _, isChecked ->
                    model.isSelected = isChecked
                    notifyItemChanged(position)  // Update item state
                    onCheckBoxClick(model, position)
                }
            }
        }


        override fun getItemCount(): Int {
        return list.size
    }

    fun refresh(newList: ArrayList<MaterialRespListModel>) {
        list.clear()
        list.addAll(newList)
        notifyDataSetChanged()
    }
        private fun calculateTotal(material: MaterialRespListModel): Double {
            val units = material.units?.toDouble() ?: 0.0  // Convert Int? to Double safely
            val cost = material.cost?.toDoubleOrNull() ?: 0.0  // Convert String? to Double safely
            return units * cost
        }

}
    
    