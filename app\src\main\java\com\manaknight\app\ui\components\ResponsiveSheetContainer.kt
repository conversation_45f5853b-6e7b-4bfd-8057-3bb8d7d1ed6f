package com.manaknight.app.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.SheetState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.manaknight.app.ui.utils.isTabletLayout

/**
 * A responsive container that shows bottom sheets on phones but displays them as centered dialogs on tablets.
 *
 * On tablets (≥600dp width):
 * - Shows a centered dialog with 600dp width
 * - Header content spans full width (600dp)
 * - Main content is constrained to 500dp width
 *
 * On phones:
 * - Shows a standard bottom sheet
 * - All content uses full width
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ResponsiveSheetContainer(
    showSheet: Boolean,
    onDismiss: () -> Unit,
    sheetState: SheetState,
    headerContent: @Composable () -> Unit,
    content: @Composable () -> Unit,
    modifier: Modifier = Modifier,
    fraction: Float = 0.6f,
    height:Dp = 400.dp
) {
    val isTablet = isTabletLayout()

    if (showSheet) {
        if (isTablet) {
            // Tablet: Show as centered dialog
            Dialog(
                onDismissRequest = onDismiss,
                properties = DialogProperties(
                    usePlatformDefaultWidth = false
                )
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(Color.Black.copy(alpha = 0.5f))
                ) {
                    Card(
                        modifier = Modifier
                            .align(Alignment.Center)
                            .width(600.dp)
                            .then(modifier),
                        shape = RoundedCornerShape(16.dp),
                        colors = CardDefaults.cardColors(containerColor = Color.White)
                    ) {
                        Column {
                            // Header content spans full width (600dp)
                            Box(
                                modifier = Modifier.fillMaxWidth()
                            ) {
                                headerContent()
                            }

                            // Main content constrained to 500dp width
                            Box(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(horizontal = 50.dp) // (600dp - 500dp) / 2 = 50dp padding on each side
                            ) {
                                content()
                            }
                        }
                    }
                }
            }
        } else {
            // Phone: Show as bottom sheet
            ModalBottomSheet(
                onDismissRequest = onDismiss,
                sheetState = sheetState,
                containerColor = Color.White,
                shape = RoundedCornerShape(topStart = 8.dp, topEnd = 8.dp),
                modifier = modifier
                    .fillMaxHeight()
            ) {
                Column(
                    modifier = Modifier.fillMaxHeight()
                ) {
                    // On phones, both header and content use full width
                    headerContent()
                    content()
                }
            }
        }
    }
}
